from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from orders.models import Customer, Order, OrderItem
from mixtures.models import FeedMixture


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للطلبات والعملاء'

    def add_arguments(self, parser):
        parser.add_argument(
            '--customers',
            type=int,
            default=10,
            help='عدد العملاء المراد إنشاؤها (افتراضي: 10)'
        )
        parser.add_argument(
            '--orders',
            type=int,
            default=20,
            help='عدد الطلبات المراد إنشاؤها (افتراضي: 20)'
        )

    def handle(self, *args, **options):
        customers_count = options['customers']
        orders_count = options['orders']

        self.stdout.write(
            self.style.SUCCESS(f'بدء إنشاء {customers_count} عميل و {orders_count} طلب...')
        )

        # إنشاء العملاء
        customers = self.create_customers(customers_count)
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {len(customers)} عميل بنجاح')
        )

        # إنشاء الطلبات
        orders = self.create_orders(orders_count, customers)
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {len(orders)} طلب بنجاح')
        )

        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )

    def create_customers(self, count):
        """إنشاء عملاء تجريبيين"""
        customers_data = [
            {
                'name': 'أحمد محمد علي',
                'company_name': 'مزرعة الأمل للدواجن',
                'phone': '07901234567',
                'email': '<EMAIL>',
                'city': 'بغداد',
                'address': 'منطقة الدورة - شارع الصناعة',
                'credit_limit': Decimal('50000.000'),
                'current_balance': Decimal('-5000.000'),
            },
            {
                'name': 'فاطمة حسن محمود',
                'company_name': 'مؤسسة النور للثروة الحيوانية',
                'phone': '07801234567',
                'email': '<EMAIL>',
                'city': 'البصرة',
                'address': 'منطقة الزبير - الطريق الصناعي',
                'credit_limit': Decimal('75000.000'),
                'current_balance': Decimal('2000.000'),
            },
            {
                'name': 'محمد عبد الله الكريم',
                'company_name': 'مزارع الفرات للأسماك',
                'phone': '07701234567',
                'email': '<EMAIL>',
                'city': 'النجف',
                'address': 'ناحية الكوفة - منطقة المزارع',
                'credit_limit': Decimal('30000.000'),
                'current_balance': Decimal('0.000'),
            },
            {
                'name': 'زينب أحمد صالح',
                'company_name': 'مزرعة الخير للأغنام',
                'phone': '07601234567',
                'email': '<EMAIL>',
                'city': 'أربيل',
                'address': 'طريق أربيل - كركوك',
                'credit_limit': Decimal('40000.000'),
                'current_balance': Decimal('-1500.000'),
            },
            {
                'name': 'علي حسين جعفر',
                'company_name': 'شركة دجلة للأعلاف',
                'phone': '07501234567',
                'email': '<EMAIL>',
                'city': 'الموصل',
                'address': 'الضفة اليسرى - المنطقة الصناعية',
                'credit_limit': Decimal('100000.000'),
                'current_balance': Decimal('10000.000'),
            },
        ]

        # إضافة عملاء إضافيين إذا كان العدد المطلوب أكبر
        cities = ['بغداد', 'البصرة', 'أربيل', 'الموصل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى']
        names = [
            'سارة محمد', 'حسام علي', 'نور فاضل', 'عمر حسن', 'ليلى أحمد',
            'كريم عبد الله', 'هدى محمود', 'يوسف إبراهيم', 'مريم صالح', 'طارق جاسم'
        ]

        customers = []
        for i in range(count):
            if i < len(customers_data):
                data = customers_data[i]
            else:
                # إنشاء بيانات عشوائية للعملاء الإضافيين
                name = random.choice(names)
                data = {
                    'name': f'{name} {i+1}',
                    'company_name': f'مؤسسة {name} التجارية',
                    'phone': f'079{random.randint(1000000, 9999999)}',
                    'email': f'customer{i+1}@example.com',
                    'city': random.choice(cities),
                    'address': f'العنوان التجريبي {i+1}',
                    'credit_limit': Decimal(str(random.randint(10000, 100000))),
                    'current_balance': Decimal(str(random.randint(-5000, 10000))),
                }

            customer, created = Customer.objects.get_or_create(
                phone=data['phone'],
                defaults=data
            )
            customers.append(customer)

        return customers

    def create_orders(self, count, customers):
        """إنشاء طلبات تجريبية"""
        # الحصول على المستخدم الأول (المدير)
        try:
            user = User.objects.first()
            if not user:
                self.stdout.write(
                    self.style.ERROR('لا يوجد مستخدمين في النظام. يرجى إنشاء مستخدم أولاً.')
                )
                return []
        except Exception:
            self.stdout.write(
                self.style.ERROR('خطأ في الحصول على المستخدم')
            )
            return []

        # الحصول على الخلطات المتاحة
        mixtures = list(FeedMixture.objects.filter(is_active=True))
        if not mixtures:
            self.stdout.write(
                self.style.ERROR('لا توجد خلطات متاحة. يرجى إنشاء خلطات أولاً.')
            )
            return []

        statuses = ['pending', 'confirmed', 'in_production', 'ready', 'delivered']
        payment_statuses = ['unpaid', 'partial', 'paid']

        orders = []
        for i in range(count):
            # اختيار عميل عشوائي
            customer = random.choice(customers)
            
            # تاريخ الطلب (خلال آخر 30 يوم)
            order_date = timezone.now() - timedelta(days=random.randint(0, 30))
            
            # التاريخ المطلوب (بين 1-14 يوم من تاريخ الطلب)
            required_date = order_date.date() + timedelta(days=random.randint(1, 14))

            # إنشاء الطلب
            order = Order.objects.create(
                order_number=f"ORD-{order_date.strftime('%Y%m%d')}-{i+1:03d}",
                customer=customer,
                created_by=user,
                order_date=order_date,
                required_date=required_date,
                status=random.choice(statuses),
                payment_status=random.choice(payment_statuses),
                discount_percentage=Decimal(str(random.choice([0, 2, 5, 10]))),
                delivery_address=f'عنوان التسليم التجريبي {i+1}',
                notes=f'ملاحظات تجريبية للطلب رقم {i+1}'
            )

            # إضافة عناصر للطلب (1-4 عناصر)
            num_items = random.randint(1, min(4, len(mixtures)))
            selected_mixtures = random.sample(mixtures, num_items)

            for mixture in selected_mixtures:
                quantity = Decimal(str(random.randint(50, 1000)))
                # سعر قريب من تكلفة الخلطة مع هامش ربح
                base_price = mixture.cost_per_kg or Decimal('1.000')
                unit_price = base_price * Decimal(str(random.uniform(1.1, 1.5)))

                OrderItem.objects.create(
                    order=order,
                    mixture=mixture,
                    quantity_kg=quantity,
                    unit_price=unit_price,
                    notes=f'ملاحظات تجريبية للعنصر'
                )

            orders.append(order)

        return orders
