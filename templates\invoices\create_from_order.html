{% extends 'base.html' %}

{% block title %}إنشاء فاتورة من الطلب - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:detail' order.pk %}">طلب {{ order.order_number }}</a></li>
        <li class="breadcrumb-item active">إنشاء فاتورة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice text-primary"></i>
                    إنشاء فاتورة من الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    سيتم إنشاء فاتورة جديدة بناءً على بيانات الطلب التالي
                </div>

                <!-- معلومات الطلب -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات الطلب:</h6>
                        <div class="mb-2">
                            <strong>رقم الطلب:</strong> {{ order.order_number }}
                        </div>
                        <div class="mb-2">
                            <strong>تاريخ الطلب:</strong> {{ order.order_date|date:"d/m/Y" }}
                        </div>
                        <div class="mb-2">
                            <strong>حالة الطلب:</strong>
                            <span class="badge bg-{% if order.status == 'completed' %}success{% elif order.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                {{ order.get_status_display }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات العميل:</h6>
                        <div class="mb-2">
                            <strong>اسم العميل:</strong> {{ order.customer.name }}
                        </div>
                        {% if order.customer.company_name %}
                        <div class="mb-2">
                            <strong>اسم الشركة:</strong> {{ order.customer.company_name }}
                        </div>
                        {% endif %}
                        <div class="mb-2">
                            <strong>رقم الهاتف:</strong> {{ order.customer.phone }}
                        </div>
                        {% if order.customer.email %}
                        <div class="mb-2">
                            <strong>البريد الإلكتروني:</strong> {{ order.customer.email }}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- عناصر الطلب -->
                <h6 class="text-primary mb-3">عناصر الطلب:</h6>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order.items.all %}
                            <tr>
                                <td>
                                    <strong>{{ item.mixture.name }}</strong><br>
                                    <small class="text-muted">{{ item.mixture.feed_type.name }}</small>
                                </td>
                                <td>{{ item.quantity_kg|floatformat:2 }} كغم</td>
                                <td class="currency">{{ item.unit_price|floatformat:3 }} د.أ</td>
                                <td class="currency">{{ item.total_price|floatformat:2 }} د.أ</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="3">المجموع الفرعي:</th>
                                <th class="currency">{{ order.subtotal|floatformat:2 }} د.أ</th>
                            </tr>
                            {% if order.discount_amount > 0 %}
                            <tr>
                                <th colspan="3">الخصم ({{ order.discount_percentage }}%):</th>
                                <th class="currency text-danger">-{{ order.discount_amount|floatformat:2 }} د.أ</th>
                            </tr>
                            {% endif %}
                            {% if order.tax_amount > 0 %}
                            <tr>
                                <th colspan="3">الضريبة ({{ order.tax_percentage }}%):</th>
                                <th class="currency">{{ order.tax_amount|floatformat:2 }} د.أ</th>
                            </tr>
                            {% endif %}
                            <tr class="table-primary">
                                <th colspan="3">المجموع الإجمالي:</th>
                                <th class="currency">{{ order.total_amount|floatformat:2 }} د.أ</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- معلومات الفاتورة -->
                <div class="alert alert-light">
                    <h6 class="text-primary">معلومات الفاتورة التي سيتم إنشاؤها:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>تاريخ الإصدار:</strong> {% now "d/m/Y" %}
                            </div>
                            <div class="mb-2">
                                <strong>تاريخ الاستحقاق:</strong>
                                {{ due_date|date:"d/m/Y" }}
                                <small class="text-muted">(30 يوم من اليوم)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>المبلغ الإجمالي:</strong> 
                                <span class="currency text-success">{{ order.total_amount|floatformat:2 }} د.أ</span>
                            </div>
                            <div class="mb-2">
                                <strong>الحالة الأولية:</strong> 
                                <span class="badge bg-secondary">مسودة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:detail' order.pk %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-invoice"></i>
                            إنشاء الفاتورة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تأكيد إنشاء الفاتورة
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من إنشاء فاتورة لهذا الطلب؟')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
