# Generated by Django 5.2.1 on 2025-06-03 18:52

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('ingredients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='نوع العلف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('target_animal', models.CharField(max_length=100, verbose_name='الحيوان المستهدف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نوع العلف',
                'verbose_name_plural': 'أنواع الأعلاف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FeedMixture',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, unique=True, verbose_name='اسم الخلطة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('target_protein_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة البروتين المستهدفة %')),
                ('target_energy_kcal_per_kg', models.DecimalField(decimal_places=2, default=0, max_digits=8, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الطاقة المستهدفة (كيلو كالوري/كغم)')),
                ('target_fiber_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الألياف المستهدفة %')),
                ('cost_per_kg', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=10, verbose_name='تكلفة الإنتاج لكل كغم (دينار)')),
                ('selling_price_per_kg', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر البيع لكل كغم (دينار)')),
                ('profit_margin_percentage', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=5, verbose_name='هامش الربح %')),
                ('batch_size_kg', models.DecimalField(decimal_places=2, default=1000, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='حجم الدفعة (كغم)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('feed_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mixtures', to='mixtures.feedtype', verbose_name='نوع العلف')),
            ],
            options={
                'verbose_name': 'خلطة علف',
                'verbose_name_plural': 'خلطات الأعلاف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MixtureComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='النسبة %')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('ingredient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ingredients.ingredient', verbose_name='المكون')),
                ('mixture', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='mixtures.feedmixture', verbose_name='الخلطة')),
            ],
            options={
                'verbose_name': 'مكون الخلطة',
                'verbose_name_plural': 'مكونات الخلطة',
                'ordering': ['-percentage'],
                'unique_together': {('mixture', 'ingredient')},
            },
        ),
    ]
