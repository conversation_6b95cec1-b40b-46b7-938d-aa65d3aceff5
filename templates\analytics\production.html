{% extends 'base.html' %}

{% block title %}دفعات الإنتاج - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'analytics:dashboard' %}">التحليلات والتقارير</a></li>
        <li class="breadcrumb-item active">دفعات الإنتاج</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-industry text-success"></i>
                دفعات الإنتاج
            </h1>
            <div>
                <a href="{% url 'analytics:production_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة دفعة إنتاج
                </a>
                <a href="{% url 'analytics:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للتحليلات
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">قائمة دفعات الإنتاج</h5>
            </div>
            <div class="card-body">
                {% if batches %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>الخلطة</th>
                                    <th>الكمية المنتجة</th>
                                    <th>تاريخ الإنتاج</th>
                                    <th>المنتج بواسطة</th>
                                    <th>التكلفة الإجمالية</th>
                                    <th>تكلفة الكيلو</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr>
                                    <td>
                                        <strong>{{ batch.batch_number }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ batch.mixture.name }}</strong>
                                            <br><small class="text-muted">{{ batch.mixture.feed_type.name }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ batch.quantity_produced_kg|floatformat:0 }} كغم</span>
                                    </td>
                                    <td>{{ batch.production_date|date:"d/m/Y" }}</td>
                                    <td>
                                        {% if batch.produced_by %}
                                            {{ batch.produced_by.get_full_name|default:batch.produced_by.username }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td class="currency">{{ batch.total_cost|floatformat:2 }} د.أ</td>
                                    <td class="currency">{{ batch.cost_per_kg|floatformat:3 }} د.أ</td>
                                    <td>
                                        <span class="badge bg-success">مكتملة</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-industry fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد دفعات إنتاج</h5>
                        <p class="text-muted">ابدأ بإضافة دفعات الإنتاج لتتبع العمليات الإنتاجية</p>
                        <a href="{% url 'analytics:production_add' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة دفعة إنتاج
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الإنتاج -->
{% if batches %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ batches.count }}</h4>
                        <span>إجمالي الدفعات</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_production|floatformat:0 }}</h4>
                        <span>إجمالي الإنتاج (كغم)</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-weight fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_cost|floatformat:0 }}</h4>
                        <span>إجمالي التكلفة (د.أ)</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ avg_cost_per_kg|floatformat:2 }}</h4>
                        <span>متوسط التكلفة/كغم (د.أ)</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calculator fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
