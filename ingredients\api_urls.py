from django.urls import path
from . import api_views

app_name = 'ingredients_api'

urlpatterns = [
    # فئات المكونات
    path('categories/', api_views.IngredientCategoryListCreateView.as_view(), name='category-list'),
    path('categories/<int:pk>/', api_views.IngredientCategoryDetailView.as_view(), name='category-detail'),
    
    # المكونات
    path('', api_views.IngredientListCreateView.as_view(), name='ingredient-list'),
    path('<int:pk>/', api_views.IngredientDetailView.as_view(), name='ingredient-detail'),
    
    # إحصائيات ووظائف إضافية
    path('stats/', api_views.ingredient_stats, name='ingredient-stats'),
    path('<int:pk>/update-stock/', api_views.update_stock, name='update-stock'),
    path('low-stock/', api_views.low_stock_ingredients, name='low-stock'),
]
