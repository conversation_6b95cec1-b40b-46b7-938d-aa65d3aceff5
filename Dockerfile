# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        gettext \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY . /app/

# إنشاء مجلدات الملفات الثابتة والوسائط
RUN mkdir -p /app/staticfiles /app/mediafiles /app/logs

# جمع الملفات الثابتة
RUN python manage.py collectstatic --noinput

# تعيين الصلاحيات
RUN chmod +x /app/entrypoint.sh

# تشغيل النظام
EXPOSE 8000
ENTRYPOINT ["/app/entrypoint.sh"]
