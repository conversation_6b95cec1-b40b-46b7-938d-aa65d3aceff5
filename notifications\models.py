from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class Notification(models.Model):
    """نموذج الإشعارات"""

    NOTIFICATION_TYPES = [
        ('info', _('معلومات')),
        ('warning', _('تحذير')),
        ('success', _('نجاح')),
        ('error', _('خطأ')),
        ('order', _('طلب جديد')),
        ('payment', _('دفعة جديدة')),
        ('stock', _('تنبيه مخزون')),
        ('invoice', _('فاتورة جديدة')),
    ]

    PRIORITY_LEVELS = [
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجلة')),
    ]

    # المستقبل
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('المستخدم')
    )

    # محتوى الإشعار
    title = models.CharField(_('العنوان'), max_length=200)
    message = models.TextField(_('الرسالة'))
    notification_type = models.CharField(
        _('نوع الإشعار'),
        max_length=20,
        choices=NOTIFICATION_TYPES,
        default='info'
    )
    priority = models.CharField(
        _('الأولوية'),
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium'
    )

    # الروابط والإجراءات
    action_url = models.URLField(_('رابط الإجراء'), blank=True, null=True)
    action_text = models.CharField(_('نص الإجراء'), max_length=100, blank=True, null=True)

    # حالة الإشعار
    is_read = models.BooleanField(_('مقروء'), default=False)
    read_at = models.DateTimeField(_('تاريخ القراءة'), blank=True, null=True)

    # معلومات إضافية
    data = models.JSONField(_('بيانات إضافية'), default=dict, blank=True)

    # تواريخ
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    expires_at = models.DateTimeField(_('تاريخ الانتهاء'), blank=True, null=True)

    class Meta:
        verbose_name = _('إشعار')
        verbose_name_plural = _('الإشعارات')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['created_at']),
            models.Index(fields=['notification_type']),
        ]

    def __str__(self):
        return f"{self.title} - {self.user.username}"

    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    @property
    def is_expired(self):
        """هل انتهت صلاحية الإشعار"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def time_since_created(self):
        """الوقت منذ إنشاء الإشعار"""
        delta = timezone.now() - self.created_at

        if delta.days > 0:
            return f"منذ {delta.days} يوم"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return f"منذ {hours} ساعة"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"


class NotificationTemplate(models.Model):
    """قوالب الإشعارات"""

    name = models.CharField(_('اسم القالب'), max_length=100, unique=True)
    title_template = models.CharField(_('قالب العنوان'), max_length=200)
    message_template = models.TextField(_('قالب الرسالة'))
    notification_type = models.CharField(
        _('نوع الإشعار'),
        max_length=20,
        choices=Notification.NOTIFICATION_TYPES,
        default='info'
    )
    priority = models.CharField(
        _('الأولوية'),
        max_length=10,
        choices=Notification.PRIORITY_LEVELS,
        default='medium'
    )

    # إعدادات الإرسال
    send_email = models.BooleanField(_('إرسال بريد إلكتروني'), default=False)
    send_sms = models.BooleanField(_('إرسال رسالة نصية'), default=False)
    send_push = models.BooleanField(_('إرسال إشعار فوري'), default=True)

    # تواريخ
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('قالب إشعار')
        verbose_name_plural = _('قوالب الإشعارات')

    def __str__(self):
        return self.name

    def render(self, context=None):
        """تطبيق القالب مع السياق"""
        if context is None:
            context = {}

        title = self.title_template.format(**context)
        message = self.message_template.format(**context)

        return {
            'title': title,
            'message': message,
            'notification_type': self.notification_type,
            'priority': self.priority,
        }


class NotificationSettings(models.Model):
    """إعدادات الإشعارات للمستخدمين"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='notification_settings',
        verbose_name=_('المستخدم')
    )

    # إعدادات عامة
    email_notifications = models.BooleanField(_('إشعارات البريد الإلكتروني'), default=True)
    sms_notifications = models.BooleanField(_('الرسائل النصية'), default=False)
    push_notifications = models.BooleanField(_('الإشعارات الفورية'), default=True)

    # إعدادات حسب النوع
    order_notifications = models.BooleanField(_('إشعارات الطلبات'), default=True)
    payment_notifications = models.BooleanField(_('إشعارات الدفعات'), default=True)
    stock_notifications = models.BooleanField(_('تنبيهات المخزون'), default=True)
    invoice_notifications = models.BooleanField(_('إشعارات الفواتير'), default=True)

    # أوقات الإرسال
    quiet_hours_start = models.TimeField(_('بداية الساعات الهادئة'), default='22:00')
    quiet_hours_end = models.TimeField(_('نهاية الساعات الهادئة'), default='08:00')

    # تواريخ
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعدادات الإشعارات')
        verbose_name_plural = _('إعدادات الإشعارات')

    def __str__(self):
        return f"إعدادات {self.user.username}"
