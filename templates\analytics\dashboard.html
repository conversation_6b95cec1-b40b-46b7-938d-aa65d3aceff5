{% extends 'base.html' %}

{% block title %}التحليلات والتقارير - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">التحليلات والتقارير</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-chart-line text-primary"></i>
                التحليلات والتقارير
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i>
                    تصدير التقارير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'analytics:export_pdf' %}">
                        <i class="fas fa-file-pdf text-danger"></i> تصدير PDF
                    </a></li>
                    <li><a class="dropdown-item" href="{% url 'analytics:export_excel' %}">
                        <i class="fas fa-file-excel text-success"></i> تصدير Excel
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الإيرادات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales_stats.total_revenue|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي الطلبات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales_stats.total_orders }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي الإنتاج
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ production_stats.total_production|floatformat:0 }} كغم
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-industry fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الطلبات المعلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales_stats.pending_orders }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row">
    <!-- رسم بياني للمبيعات -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">نظرة عامة على المبيعات</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#">عرض التفاصيل</a>
                        <a class="dropdown-item" href="#">تصدير البيانات</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- رسم بياني دائري للفئات -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">توزيع المبيعات حسب الفئة</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- التقارير التفصيلية -->
<div class="row">
    <!-- أحدث دفعات الإنتاج -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">أحدث دفعات الإنتاج</h6>
                <a href="{% url 'analytics:production' %}" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_batches %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>الخلطة</th>
                                    <th>الكمية</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in recent_batches %}
                                <tr>
                                    <td>{{ batch.batch_number }}</td>
                                    <td>{{ batch.mixture.name }}</td>
                                    <td>{{ batch.quantity_produced_kg|floatformat:0 }} كغم</td>
                                    <td>{{ batch.production_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-industry fa-3x mb-3"></i>
                        <p>لا توجد دفعات إنتاج بعد</p>
                        <a href="{% url 'analytics:production_add' %}" class="btn btn-primary">إضافة دفعة إنتاج</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- أفضل المنتجات مبيعاً -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">أفضل المنتجات مبيعاً</h6>
                <a href="{% url 'analytics:sales_report' %}" class="btn btn-sm btn-primary">عرض التقرير</a>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.total_sold|floatformat:0 }} كغم</td>
                                    <td>{{ product.total_revenue|floatformat:2 }} د.أ</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <p>لا توجد بيانات مبيعات بعد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة للتقارير -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">التقارير المتاحة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'analytics:sales_report' %}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-chart-line"></i><br>
                            تقرير المبيعات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'analytics:production' %}" class="btn btn-outline-success btn-block">
                            <i class="fas fa-industry"></i><br>
                            تقرير الإنتاج
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-info btn-block">
                            <i class="fas fa-warehouse"></i><br>
                            تقرير المخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-dollar-sign"></i><br>
                            التقرير المالي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.btn-block {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'المبيعات (د.أ)',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'تطور المبيعات الشهرية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني دائري للفئات
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryChart = new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: ['أعلاف الدواجن', 'أعلاف الأبقار', 'أعلاف الأغنام', 'أخرى'],
        datasets: [{
            data: [40, 30, 20, 10],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
