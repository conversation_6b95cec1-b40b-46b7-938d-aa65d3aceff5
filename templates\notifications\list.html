{% extends 'base.html' %}

{% block title %}الإشعارات - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">الإشعارات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-bell text-primary"></i>
                الإشعارات
            </h1>
            <div>
                {% if unread_count > 0 %}
                <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i>
                    تحديد الكل كمقروء ({{ unread_count }})
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                {% if notifications %}
                    <div class="list-group list-group-flush">
                        {% for notification in notifications %}
                        <div class="list-group-item {% if not notification.is_read %}list-group-item-light border-start border-primary border-3{% endif %}">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div class="d-flex">
                                    <div class="notification-icon me-3 mt-1">
                                        {% if notification.notification_type == 'order' %}
                                            <i class="fas fa-shopping-cart fa-lg text-primary"></i>
                                        {% elif notification.notification_type == 'payment' %}
                                            <i class="fas fa-dollar-sign fa-lg text-success"></i>
                                        {% elif notification.notification_type == 'stock' %}
                                            <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                                        {% elif notification.notification_type == 'invoice' %}
                                            <i class="fas fa-file-invoice fa-lg text-info"></i>
                                        {% elif notification.notification_type == 'success' %}
                                            <i class="fas fa-check-circle fa-lg text-success"></i>
                                        {% elif notification.notification_type == 'warning' %}
                                            <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                                        {% elif notification.notification_type == 'error' %}
                                            <i class="fas fa-times-circle fa-lg text-danger"></i>
                                        {% else %}
                                            <i class="fas fa-info-circle fa-lg text-secondary"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                                {{ notification.title }}
                                                {% if not notification.is_read %}
                                                    <span class="badge bg-primary ms-2">جديد</span>
                                                {% endif %}
                                            </h6>
                                            <small class="text-muted">{{ notification.time_since_created }}</small>
                                        </div>
                                        <p class="mb-2 text-muted">{{ notification.message }}</p>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                {% if notification.priority == 'urgent' %}
                                                    <span class="badge bg-danger">عاجل</span>
                                                {% elif notification.priority == 'high' %}
                                                    <span class="badge bg-warning">عالي</span>
                                                {% elif notification.priority == 'medium' %}
                                                    <span class="badge bg-info">متوسط</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">منخفض</span>
                                                {% endif %}
                                                
                                                <small class="text-muted ms-2">
                                                    {{ notification.created_at|date:"d/m/Y H:i" }}
                                                </small>
                                            </div>
                                            
                                            <div>
                                                {% if notification.action_url and notification.action_text %}
                                                    <a href="{{ notification.action_url }}" class="btn btn-sm btn-outline-primary me-2">
                                                        {{ notification.action_text }}
                                                    </a>
                                                {% endif %}
                                                
                                                {% if not notification.is_read %}
                                                    <button class="btn btn-sm btn-outline-success" onclick="markAsRead({{ notification.pk }})">
                                                        <i class="fas fa-check"></i>
                                                        تحديد كمقروء
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إشعارات</h5>
                        <p class="text-muted">ستظهر الإشعارات هنا عند توفرها</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notification-icon {
    width: 40px;
    text-align: center;
}

.list-group-item {
    border-left: none !important;
    border-right: none !important;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item.border-start {
    border-left: 4px solid var(--bs-primary) !important;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
