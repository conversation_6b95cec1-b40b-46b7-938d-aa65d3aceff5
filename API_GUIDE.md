# 📚 دليل API نظام مصنع الأعلاف

## 🔗 المعلومات الأساسية

**Base URL:** `http://localhost:8004/api/`

جميع نقاط API تتطلب مصادقة المستخدم عبر Django Session Authentication.

---

## 🌾 API المكونات

### 📋 قائمة المكونات
```http
GET /api/ingredients/
```

**المعاملات:**
- `search`: البحث في الاسم والوصف والمورد
- `ordering`: ترتيب النتائج (`name`, `cost_per_kg`, `current_stock`, `created_at`)
- `page`: رقم الصفحة

**مثال على الاستجابة:**
```json
{
  "count": 8,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "ذرة صفراء",
      "category": 1,
      "category_name": "الحبوب",
      "protein_percentage": 8.5,
      "energy_kcal_per_kg": 3350,
      "cost_per_kg": "0.450",
      "current_stock": "5000.00",
      "minimum_stock": "1000.00",
      "stock_status": "متوفر",
      "is_low_stock": false,
      "is_active": true
    }
  ]
}
```

### ➕ إضافة مكون جديد
```http
POST /api/ingredients/
Content-Type: application/json

{
  "name": "اسم المكون",
  "category": 1,
  "description": "وصف المكون",
  "protein_percentage": 15.5,
  "energy_kcal_per_kg": 3200,
  "fiber_percentage": 3.2,
  "fat_percentage": 4.1,
  "ash_percentage": 6.0,
  "moisture_percentage": 12.0,
  "cost_per_kg": "0.650",
  "current_stock": "2000.00",
  "minimum_stock": "500.00",
  "supplier": "اسم المورد",
  "notes": "ملاحظات",
  "is_active": true
}
```

### 📊 إحصائيات المكونات
```http
GET /api/ingredients/stats/
```

**الاستجابة:**
```json
{
  "total_ingredients": 8,
  "total_categories": 5,
  "stock_status": {
    "available": 8,
    "low_stock": 0,
    "out_of_stock": 0
  },
  "cost_stats": {
    "average_cost_per_kg": 0.761,
    "total_stock_value": 7020.000
  }
}
```

### 📦 تحديث المخزون
```http
POST /api/ingredients/{id}/update-stock/
Content-Type: application/json

{
  "action": "add",  // "add", "subtract", "set"
  "quantity": 100.5
}
```

---

## 🧪 API الخلطات

### 📋 قائمة الخلطات
```http
GET /api/mixtures/
```

**المعاملات:**
- `search`: البحث في الاسم والوصف
- `ordering`: ترتيب النتائج
- `formula_complete`: فلتر الصيغ المكتملة (`true`/`false`)

### ➕ إضافة خلطة جديدة
```http
POST /api/mixtures/
Content-Type: application/json

{
  "name": "اسم الخلطة",
  "feed_type": 1,
  "description": "وصف الخلطة",
  "target_protein_percentage": 18.0,
  "target_energy_kcal_per_kg": 2800,
  "target_fiber_percentage": 15.0,
  "selling_price_per_kg": "0.850",
  "batch_size_kg": "1000.00",
  "components": [
    {
      "ingredient_id": 1,
      "percentage": 60.0,
      "notes": "المكون الأساسي"
    }
  ],
  "notes": "ملاحظات الخلطة",
  "is_active": true
}
```

### 📊 إحصائيات الخلطات
```http
GET /api/mixtures/stats/
```

### 🧮 حساب تكلفة الخلطة
```http
POST /api/mixtures/{id}/calculate/
```

---

## 🏷️ API فئات المكونات

### 📋 قائمة الفئات
```http
GET /api/ingredients/categories/
```

### ➕ إضافة فئة جديدة
```http
POST /api/ingredients/categories/
Content-Type: application/json

{
  "name": "اسم الفئة",
  "description": "وصف الفئة"
}
```

---

## 🎯 API أنواع الأعلاف

### 📋 قائمة أنواع الأعلاف
```http
GET /api/mixtures/feed-types/
```

### ➕ إضافة نوع علف جديد
```http
POST /api/mixtures/feed-types/
Content-Type: application/json

{
  "name": "اسم نوع العلف",
  "target_animal": "الحيوان المستهدف",
  "description": "وصف نوع العلف"
}
```

---

## 📋 API الطلبات

### 📋 قائمة الطلبات
```http
GET /api/orders/
```

### 👥 قائمة العملاء
```http
GET /api/orders/customers/
```

### 📊 إحصائيات الطلبات
```http
GET /api/orders/stats/
```

---

## 📈 API التحليلات

### 📊 الإحصائيات الشاملة
```http
GET /api/analytics/stats/
```

### 🏭 دفعات الإنتاج
```http
GET /api/analytics/production/
```

---

## 🔍 البحث والتصفية

### البحث النصي
```http
GET /api/ingredients/?search=ذرة
```

### الترتيب
```http
GET /api/ingredients/?ordering=name
GET /api/ingredients/?ordering=-cost_per_kg  # ترتيب عكسي
```

### التصفح
```http
GET /api/ingredients/?page=2
```

---

## 🛠️ أمثلة عملية

### إضافة مكون جديد
```bash
curl -X POST http://localhost:8004/api/ingredients/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "شعير",
    "category": 1,
    "protein_percentage": 11.5,
    "energy_kcal_per_kg": 2900,
    "cost_per_kg": "0.380",
    "current_stock": "3000.00",
    "minimum_stock": "800.00"
  }'
```

### البحث عن مكونات
```bash
curl "http://localhost:8004/api/ingredients/?search=ذرة&ordering=cost_per_kg"
```

### تحديث المخزون
```bash
curl -X POST http://localhost:8004/api/ingredients/1/update-stock/ \
  -H "Content-Type: application/json" \
  -d '{
    "action": "add",
    "quantity": 500
  }'
```

---

## 📝 رموز الاستجابة

- `200 OK`: نجح الطلب
- `201 Created`: تم إنشاء المورد بنجاح
- `400 Bad Request`: خطأ في البيانات المرسلة
- `401 Unauthorized`: غير مصرح بالوصول
- `404 Not Found`: المورد غير موجود
- `500 Internal Server Error`: خطأ في الخادم

---

## 🚀 نصائح للاستخدام

1. **استخدم HTTPS في الإنتاج**
2. **احفظ رموز الاستجابة للتعامل مع الأخطاء**
3. **استخدم التصفح للقوائم الطويلة**
4. **اختبر API باستخدام أدوات مثل Postman أو curl**
5. **راجع رسائل الخطأ للحصول على تفاصيل المشاكل**

---

## ✅ نتائج الاختبار

تم اختبار API بنجاح وجميع النقاط التالية تعمل:

✅ **المكونات:**
- قائمة المكونات (8 مكونات)
- إحصائيات المكونات
- البحث والتصفية

✅ **الخلطات:**
- قائمة الخلطات (1 خلطة)
- إحصائيات الخلطات

✅ **الطلبات:**
- قائمة الطلبات (فارغة)

⚠️ **التحليلات:**
- خطأ في الإحصائيات الشاملة (500) - يحتاج إصلاح

النظام جاهز للاستخدام مع معظم الوظائف تعمل بشكل صحيح!
