{% extends 'base.html' %}

{% block title %}تفاصيل العميل: {{ customer.name }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:customers' %}">العملاء</a></li>
        <li class="breadcrumb-item active">{{ customer.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user"></i>
                تفاصيل العميل: {{ customer.name }}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'orders:customer_edit' customer.pk %}" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i>
                    تعديل
                </a>
                <a href="{% url 'orders:add' %}?customer={{ customer.pk }}" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </a>
                <a href="{% url 'orders:customer_delete' customer.pk %}" class="btn btn-outline-danger">
                    <i class="fas fa-trash"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات العميل -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم العميل:</strong> {{ customer.name }}</p>
                        {% if customer.company_name %}
                        <p><strong>اسم الشركة:</strong> {{ customer.company_name }}</p>
                        {% endif %}
                        <p><strong>رقم الهاتف:</strong> {{ customer.phone }}</p>
                        {% if customer.email %}
                        <p><strong>البريد الإلكتروني:</strong>
                            <a href="mailto:{{ customer.email }}">{{ customer.email }}</a>
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if customer.address %}
                        <p><strong>العنوان:</strong> {{ customer.address }}</p>
                        {% endif %}
                        {% if customer.city %}
                        <p><strong>المدينة:</strong> {{ customer.city }}</p>
                        {% endif %}
                        <p><strong>تاريخ التسجيل:</strong> {{ customer.created_at|date:"Y/m/d" }}</p>
                        <p><strong>الحالة:</strong>
                            {% if customer.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if customer.notes %}
                <hr>
                <p><strong>ملاحظات:</strong></p>
                <p class="text-muted">{{ customer.notes }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- معلومات مالية -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator"></i>
                    المعلومات المالية
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>حد الائتمان:</span>
                    <span class="currency">{{ customer.credit_limit|floatformat:3 }} د.أ</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الرصيد الحالي:</span>
                    <span class="currency {% if customer.current_balance < 0 %}text-danger{% elif customer.current_balance > 0 %}text-success{% endif %}">
                        {{ customer.current_balance|floatformat:3 }} د.أ
                    </span>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>الائتمان المتاح:</strong>
                    <strong class="currency {% if customer.available_credit < 0 %}text-danger{% else %}text-success{% endif %}">
                        {{ customer.available_credit|floatformat:3 }} د.ع
                    </strong>
                </div>
            </div>
        </div>

        <!-- إحصائيات العميل -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الطلبات:</span>
                    <span class="badge bg-primary">{{ stats.total_orders }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الطلبات المعلقة:</span>
                    <span class="badge bg-warning">{{ stats.pending_orders }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>إجمالي المشتريات:</span>
                    <span class="currency">{{ stats.total_amount|floatformat:3 }} د.أ</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- طلبات العميل -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-shopping-cart"></i>
                طلبات العميل (آخر 10 طلبات)
            </h6>
            <a href="{% url 'orders:add' %}?customer={{ customer.pk }}" class="btn btn-sm btn-success">
                <i class="fas fa-plus"></i>
                طلب جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        {% if orders %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>تاريخ الطلب</th>
                        <th>التاريخ المطلوب</th>
                        <th>حالة الطلب</th>
                        <th>حالة الدفع</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                    <tr>
                        <td>
                            <a href="{% url 'orders:detail' order.pk %}" class="text-decoration-none">
                                <strong>{{ order.order_number }}</strong>
                            </a>
                        </td>
                        <td>{{ order.order_date|date:"Y/m/d" }}</td>
                        <td>{{ order.required_date|date:"Y/m/d" }}</td>
                        <td>
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">مؤكد</span>
                            {% elif order.status == 'in_production' %}
                                <span class="badge bg-primary">قيد الإنتاج</span>
                            {% elif order.status == 'ready' %}
                                <span class="badge bg-secondary">جاهز</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.payment_status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوع</span>
                            {% elif order.payment_status == 'partial' %}
                                <span class="badge bg-warning">مدفوع جزئياً</span>
                            {% elif order.payment_status == 'paid' %}
                                <span class="badge bg-success">مدفوع بالكامل</span>
                            {% endif %}
                        </td>
                        <td><strong class="currency">{{ order.total_amount|floatformat:3 }} د.ع</strong></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'orders:detail' order.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'orders:edit' order.pk %}" class="btn btn-sm btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if stats.total_orders > 10 %}
        <div class="text-center mt-3">
            <a href="{% url 'orders:list' %}?customer={{ customer.pk }}" class="btn btn-outline-primary">
                <i class="fas fa-list"></i>
                عرض جميع الطلبات ({{ stats.total_orders }})
            </a>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد طلبات لهذا العميل</h5>
            <p class="text-muted">ابدأ بإنشاء طلب جديد للعميل</p>
            <a href="{% url 'orders:add' %}?customer={{ customer.pk }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إنشاء طلب جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'orders:customers' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة لقائمة العملاء
        </a>
    </div>
</div>
{% endblock %}
