from rest_framework import generics, filters, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Avg
from .models import FeedType, FeedMixture, MixtureComponent
from .serializers import (
    FeedTypeSerializer, FeedMixtureListSerializer,
    FeedMixtureDetailSerializer, FeedMixtureCreateUpdateSerializer,
    MixtureComponentSerializer, MixtureComponentCreateUpdateSerializer
)


class FeedTypeListCreateView(generics.ListCreateAPIView):
    """قائمة وإنشاء أنواع الأعلاف"""
    queryset = FeedType.objects.all()
    serializer_class = FeedTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'target_animal', 'description']
    ordering_fields = ['name', 'target_animal', 'created_at']
    ordering = ['name']


class FeedTypeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """تفاصيل وتحديث وحذف نوع العلف"""
    queryset = FeedType.objects.all()
    serializer_class = FeedTypeSerializer
    permission_classes = [IsAuthenticated]


class FeedMixtureListCreateView(generics.ListCreateAPIView):
    """قائمة وإنشاء الخلطات"""
    queryset = FeedMixture.objects.select_related('feed_type')
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'cost_per_kg', 'selling_price_per_kg', 'profit_margin_percentage', 'created_at']
    ordering = ['name']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return FeedMixtureCreateUpdateSerializer
        return FeedMixtureListSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # فلتر حسب اكتمال الصيغة
        formula_complete = self.request.query_params.get('formula_complete')
        if formula_complete is not None:
            if formula_complete.lower() == 'true':
                queryset = [mixture for mixture in queryset if mixture.is_formula_complete]
            else:
                queryset = [mixture for mixture in queryset if not mixture.is_formula_complete]
        
        # فلتر حسب هامش الربح
        min_profit = self.request.query_params.get('min_profit')
        max_profit = self.request.query_params.get('max_profit')
        if min_profit:
            queryset = queryset.filter(profit_margin_percentage__gte=min_profit)
        if max_profit:
            queryset = queryset.filter(profit_margin_percentage__lte=max_profit)
        
        return queryset


class FeedMixtureDetailView(generics.RetrieveUpdateDestroyAPIView):
    """تفاصيل وتحديث وحذف الخلطة"""
    queryset = FeedMixture.objects.select_related('feed_type').prefetch_related('components__ingredient')
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return FeedMixtureCreateUpdateSerializer
        return FeedMixtureDetailSerializer
    
    def perform_destroy(self, instance):
        """حذف ناعم - تعطيل الخلطة بدلاً من حذفها"""
        instance.is_active = False
        instance.save()


class MixtureComponentListCreateView(generics.ListCreateAPIView):
    """قائمة وإنشاء مكونات الخلطة"""
    serializer_class = MixtureComponentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        mixture_id = self.kwargs['mixture_id']
        return MixtureComponent.objects.filter(
            mixture_id=mixture_id
        ).select_related('ingredient', 'mixture')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return MixtureComponentCreateUpdateSerializer
        return MixtureComponentSerializer
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        mixture_id = self.kwargs['mixture_id']
        try:
            context['mixture'] = FeedMixture.objects.get(id=mixture_id)
        except FeedMixture.DoesNotExist:
            pass
        return context
    
    def perform_create(self, serializer):
        mixture_id = self.kwargs['mixture_id']
        mixture = FeedMixture.objects.get(id=mixture_id)
        component = serializer.save(mixture=mixture)
        
        # إعادة حساب تكلفة الخلطة
        mixture.calculate_cost()


class MixtureComponentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """تفاصيل وتحديث وحذف مكون الخلطة"""
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        mixture_id = self.kwargs['mixture_id']
        return MixtureComponent.objects.filter(
            mixture_id=mixture_id
        ).select_related('ingredient', 'mixture')
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return MixtureComponentCreateUpdateSerializer
        return MixtureComponentSerializer
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        mixture_id = self.kwargs['mixture_id']
        try:
            context['mixture'] = FeedMixture.objects.get(id=mixture_id)
        except FeedMixture.DoesNotExist:
            pass
        return context
    
    def perform_update(self, serializer):
        component = serializer.save()
        # إعادة حساب تكلفة الخلطة
        component.mixture.calculate_cost()
    
    def perform_destroy(self, instance):
        mixture = instance.mixture
        instance.delete()
        # إعادة حساب تكلفة الخلطة
        mixture.calculate_cost()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def calculate_mixture_cost(request, pk):
    """حساب تكلفة الخلطة"""
    try:
        mixture = FeedMixture.objects.get(pk=pk)
    except FeedMixture.DoesNotExist:
        return Response(
            {'error': 'الخلطة غير موجودة'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    cost = mixture.calculate_cost()
    nutritional_values = mixture.calculate_nutritional_values()
    
    return Response({
        'mixture_id': mixture.id,
        'mixture_name': mixture.name,
        'cost_per_kg': float(cost),
        'selling_price_per_kg': float(mixture.selling_price_per_kg),
        'profit_margin_percentage': float(mixture.profit_margin_percentage),
        'nutritional_values': nutritional_values,
        'is_formula_complete': mixture.is_formula_complete,
        'total_components_percentage': float(mixture.total_components_percentage)
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mixture_stats(request):
    """إحصائيات الخلطات"""
    total_mixtures = FeedMixture.objects.filter(is_active=True).count()
    total_feed_types = FeedType.objects.count()
    
    # إحصائيات اكتمال الصيغة
    complete_formulas = sum(1 for mixture in FeedMixture.objects.filter(is_active=True) if mixture.is_formula_complete)
    incomplete_formulas = total_mixtures - complete_formulas
    
    # إحصائيات التكلفة والربح
    mixtures = FeedMixture.objects.filter(is_active=True)
    avg_cost = mixtures.aggregate(avg_cost=Avg('cost_per_kg'))['avg_cost'] or 0
    avg_selling_price = mixtures.aggregate(avg_price=Avg('selling_price_per_kg'))['avg_price'] or 0
    avg_profit_margin = mixtures.aggregate(avg_profit=Avg('profit_margin_percentage'))['avg_profit'] or 0
    
    # أفضل الخلطات ربحاً
    top_profitable = mixtures.filter(
        profit_margin_percentage__gt=0
    ).order_by('-profit_margin_percentage')[:5]
    
    top_profitable_data = []
    for mixture in top_profitable:
        top_profitable_data.append({
            'id': mixture.id,
            'name': mixture.name,
            'profit_margin_percentage': float(mixture.profit_margin_percentage),
            'cost_per_kg': float(mixture.cost_per_kg),
            'selling_price_per_kg': float(mixture.selling_price_per_kg)
        })
    
    return Response({
        'total_mixtures': total_mixtures,
        'total_feed_types': total_feed_types,
        'formula_status': {
            'complete': complete_formulas,
            'incomplete': incomplete_formulas
        },
        'financial_stats': {
            'average_cost_per_kg': round(float(avg_cost), 3),
            'average_selling_price_per_kg': round(float(avg_selling_price), 3),
            'average_profit_margin': round(float(avg_profit_margin), 2)
        },
        'top_profitable_mixtures': top_profitable_data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def incomplete_mixtures(request):
    """الخلطات غير المكتملة"""
    mixtures = FeedMixture.objects.filter(is_active=True).select_related('feed_type')
    incomplete = [mixture for mixture in mixtures if not mixture.is_formula_complete]
    
    serializer = FeedMixtureListSerializer(incomplete, many=True)
    return Response(serializer.data)
