{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if category %}تعديل فئة المكونات{% else %}إضافة فئة جديدة{% endif %} - مصنع الأعلاف
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:list' %}">المكونات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:categories' %}">فئات المكونات</a></li>
        <li class="breadcrumb-item active">
            {% if category %}تعديل فئة{% else %}إضافة فئة جديدة{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if category %}edit{% else %}plus{% endif %}"></i>
                    {% if category %}تعديل فئة المكونات{% else %}إضافة فئة جديدة{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>

        {% if category %}
        <!-- معلومات إضافية عن الفئة -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الفئة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between mb-2">
                            <span>عدد المكونات:</span>
                            <span class="badge bg-primary">{{ category.ingredients.count }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>تاريخ الإنشاء:</span>
                            <span>{{ category.created_at|date:"d/m/Y H:i" }}</span>
                        </div>
                        {% if category.updated_at != category.created_at %}
                        <div class="d-flex justify-content-between">
                            <span>آخر تحديث:</span>
                            <span>{{ category.updated_at|date:"d/m/Y H:i" }}</span>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if category.ingredients.exists %}
                            <h6>المكونات في هذه الفئة:</h6>
                            <div class="max-height-200 overflow-auto">
                                {% for ingredient in category.ingredients.all %}
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>{{ ingredient.name }}</span>
                                        <span class="badge bg-light text-dark">{{ ingredient.current_stock|floatformat:0 }} كغم</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>لا توجد مكونات في هذه الفئة بعد</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.max-height-200 {
    max-height: 200px;
}
</style>
{% endblock %}
