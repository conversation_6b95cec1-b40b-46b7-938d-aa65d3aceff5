{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:list' %}">المكونات</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{% if ingredient %}edit{% else %}plus{% endif %}"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                {% crispy form %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // حساب إجمالي النسب المئوية
    function calculateTotalPercentage() {
        const percentageFields = [
            'id_protein_percentage',
            'id_fiber_percentage',
            'id_fat_percentage',
            'id_ash_percentage',
            'id_moisture_percentage'
        ];

        let total = 0;
        percentageFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                total += parseFloat(field.value) || 0;
            }
        });

        // عرض التحذير إذا تجاوز المجموع 100%
        const warningDiv = document.getElementById('percentage-warning');
        if (!warningDiv) {
            const warning = document.createElement('div');
            warning.id = 'percentage-warning';
            warning.className = 'alert alert-warning mt-2';
            warning.style.display = 'none';
            document.querySelector('.card-body').appendChild(warning);
        }

        const warningElement = document.getElementById('percentage-warning');
        if (total > 100) {
            warningElement.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                تحذير: مجموع النسب المئوية (${total.toFixed(2)}%) يتجاوز 100%
            `;
            warningElement.style.display = 'block';
        } else {
            warningElement.style.display = 'none';
        }
    }

    // ربط الحدث بجميع حقول النسب المئوية
    document.addEventListener('DOMContentLoaded', function() {
        const percentageFields = [
            'id_protein_percentage',
            'id_fiber_percentage',
            'id_fat_percentage',
            'id_ash_percentage',
            'id_moisture_percentage'
        ];

        percentageFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', calculateTotalPercentage);
                field.addEventListener('change', calculateTotalPercentage);
            }
        });

        // حساب المجموع عند تحميل الصفحة
        calculateTotalPercentage();
    });
</script>
{% endblock %}
