#!/usr/bin/env python
"""
Django Server Launcher
"""
import os
import sys
import django
from django.core.management import execute_from_command_line
from django.core.wsgi import get_wsgi_application

def main():
    """Run the Django development server."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
    
    print("🚀 Starting Django Development Server")
    print("=" * 50)
    
    try:
        # Setup Django
        django.setup()
        print("✅ Django setup successful")
        
        # Import settings to check configuration
        from django.conf import settings
        print(f"✅ Settings loaded: {settings.DEBUG=}")
        print(f"✅ Allowed hosts: {settings.ALLOWED_HOSTS}")
        
        # Check database
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        print("\n🌐 Server starting on http://127.0.0.1:8000/")
        print("🔑 Admin panel: http://127.0.0.1:8000/admin/")
        print("👤 Username: admin | 🔐 Password: admin123456")
        print("\nPress Ctrl+C to stop the server")
        print("-" * 50)
        
        # Start the server
        from django.core.management.commands.runserver import Command as RunServerCommand
        command = RunServerCommand()
        command.run_from_argv(['manage.py', 'runserver', '127.0.0.1:8000'])
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
