from django.contrib import admin
from .models import FeedType, FeedMixture, MixtureComponent


class MixtureComponentInline(admin.TabularInline):
    model = MixtureComponent
    extra = 1
    fields = ['ingredient', 'percentage', 'notes']


@admin.register(FeedType)
class FeedTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'target_animal', 'description', 'created_at']
    search_fields = ['name', 'target_animal', 'description']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(FeedMixture)
class FeedMixtureAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'feed_type', 'cost_per_kg', 'selling_price_per_kg',
        'profit_margin_percentage', 'is_formula_complete', 'is_active'
    ]
    list_filter = ['feed_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['selling_price_per_kg', 'is_active']
    inlines = [MixtureComponentInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'feed_type', 'description', 'is_active')
        }),
        ('المتطلبات الغذائية المستهدفة', {
            'fields': (
                'target_protein_percentage', 'target_energy_kcal_per_kg',
                'target_fiber_percentage'
            )
        }),
        ('معلومات التكلفة والسعر', {
            'fields': ('selling_price_per_kg', 'cost_per_kg', 'profit_margin_percentage'),
            'description': 'التكلفة وهامش الربح يتم حسابهما تلقائياً'
        }),
        ('معلومات الإنتاج', {
            'fields': ('batch_size_kg', 'notes')
        }),
    )

    readonly_fields = ['cost_per_kg', 'profit_margin_percentage']

    def is_formula_complete(self, obj):
        return obj.is_formula_complete
    is_formula_complete.boolean = True
    is_formula_complete.short_description = 'الصيغة مكتملة'

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        # إعادة حساب التكلفة بعد الحفظ
        obj.calculate_cost()


@admin.register(MixtureComponent)
class MixtureComponentAdmin(admin.ModelAdmin):
    list_display = ['mixture', 'ingredient', 'percentage', 'cost_contribution', 'weight_in_batch']
    list_filter = ['mixture__feed_type', 'ingredient__category']
    search_fields = ['mixture__name', 'ingredient__name']

    def cost_contribution(self, obj):
        return f"{obj.cost_contribution:.3f} د.ع"
    cost_contribution.short_description = 'مساهمة التكلفة'

    def weight_in_batch(self, obj):
        return f"{obj.weight_in_batch:.2f} كغم"
    weight_in_batch.short_description = 'الوزن في الدفعة'
