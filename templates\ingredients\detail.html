{% extends 'base.html' %}

{% block title %}{{ ingredient.name }} - المكونات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:list' %}">المكونات</a></li>
        <li class="breadcrumb-item active">{{ ingredient.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-seedling"></i>
                {{ ingredient.name }}
                {% if not ingredient.is_active %}
                    <span class="badge bg-secondary">غير نشط</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'ingredients:edit' ingredient.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit"></i>
                    تعديل
                </a>
                <a href="{% url 'ingredients:list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المعلومات الأساسية -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td class="fw-bold">اسم المكون:</td>
                        <td>{{ ingredient.name }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">الفئة:</td>
                        <td>
                            <span class="badge bg-primary">{{ ingredient.category.name }}</span>
                        </td>
                    </tr>
                    {% if ingredient.description %}
                    <tr>
                        <td class="fw-bold">الوصف:</td>
                        <td>{{ ingredient.description }}</td>
                    </tr>
                    {% endif %}
                    {% if ingredient.supplier %}
                    <tr>
                        <td class="fw-bold">المورد:</td>
                        <td>{{ ingredient.supplier }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="fw-bold">الحالة:</td>
                        <td>
                            <span class="status-badge {% if ingredient.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {% if ingredient.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="fw-bold">تاريخ الإضافة:</td>
                        <td>{{ ingredient.created_at|date:"Y/m/d H:i" }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">آخر تحديث:</td>
                        <td>{{ ingredient.updated_at|date:"Y/m/d H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- القيم الغذائية -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i>
                    القيم الغذائية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary mb-1">{{ ingredient.protein_percentage|floatformat:1 }}%</h4>
                            <small class="text-muted">البروتين</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-success mb-1">{{ ingredient.energy_kcal_per_kg|floatformat:0 }}</h4>
                            <small class="text-muted">الطاقة (ك.كالوري/كغم)</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-warning mb-1">{{ ingredient.fiber_percentage|floatformat:1 }}%</h4>
                            <small class="text-muted">الألياف</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-info mb-1">{{ ingredient.fat_percentage|floatformat:1 }}%</h4>
                            <small class="text-muted">الدهون</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-secondary mb-1">{{ ingredient.ash_percentage|floatformat:1 }}%</h4>
                            <small class="text-muted">الرماد</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary mb-1">{{ ingredient.moisture_percentage|floatformat:1 }}%</h4>
                            <small class="text-muted">الرطوبة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات التكلفة والمخزون -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse"></i>
                    المخزون والتكلفة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="currency mb-1">{{ ingredient.cost_per_kg|floatformat:3 }}</h4>
                            <small class="text-muted">التكلفة (د.أ/كغم)</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1">{{ ingredient.current_stock|floatformat:2 }}</h4>
                            <small class="text-muted">المخزون الحالي (كغم)</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1">{{ ingredient.minimum_stock|floatformat:2 }}</h4>
                            <small class="text-muted">الحد الأدنى (كغم)</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 rounded
                            {% if ingredient.current_stock <= 0 %}bg-danger text-white
                            {% elif ingredient.is_low_stock %}bg-warning
                            {% else %}bg-success text-white{% endif %}">
                            <h5 class="mb-1">{{ ingredient.stock_status }}</h5>
                            <small>حالة المخزون</small>
                        </div>
                    </div>
                </div>

                {% if ingredient.current_stock <= ingredient.minimum_stock %}
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> المخزون منخفض! يُنصح بإعادة التموين قريباً.
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الملاحظات -->
    {% if ingredient.notes %}
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ ingredient.notes|linebreaks }}</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- الخلطات التي تحتوي على هذا المكون -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-blender"></i>
                    الخلطات التي تحتوي على هذا المكون
                </h5>
            </div>
            <div class="card-body">
                {% if ingredient.mixturecomponent_set.exists %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الخلطة</th>
                                    <th>نوع العلف</th>
                                    <th>النسبة المستخدمة</th>
                                    <th>الوزن في الدفعة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for component in ingredient.mixturecomponent_set.all %}
                                <tr>
                                    <td>
                                        <a href="{% url 'mixtures:detail' component.mixture.pk %}">
                                            {{ component.mixture.name }}
                                        </a>
                                    </td>
                                    <td>{{ component.mixture.feed_type.name }}</td>
                                    <td>{{ component.percentage|floatformat:2 }}%</td>
                                    <td>{{ component.weight_in_batch|floatformat:2 }} كغم</td>
                                    <td>
                                        <a href="{% url 'mixtures:detail' component.mixture.pk %}"
                                           class="btn btn-sm btn-outline-primary">
                                            عرض الخلطة
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-blender fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا يوجد خلطات تحتوي على هذا المكون حالياً</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
