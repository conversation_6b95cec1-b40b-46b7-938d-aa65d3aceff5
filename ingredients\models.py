from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _


class IngredientCategory(models.Model):
    """فئات المكونات مثل الحبوب، البروتينات، الفيتامينات"""
    name = models.CharField(_('اسم الفئة'), max_length=100, unique=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة المكون')
        verbose_name_plural = _('فئات المكونات')
        ordering = ['name']

    def __str__(self):
        return self.name


class Ingredient(models.Model):
    """نموذج المكونات الأساسية للأعلاف"""
    name = models.CharField(_('اسم المكون'), max_length=200, unique=True)
    category = models.ForeignKey(
        IngredientCategory,
        on_delete=models.CASCADE,
        verbose_name=_('الفئة'),
        related_name='ingredients'
    )
    description = models.TextField(_('الوصف'), blank=True, null=True)

    # القيم الغذائية (بالنسبة المئوية)
    protein_percentage = models.DecimalField(
        _('نسبة البروتين %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )
    energy_kcal_per_kg = models.DecimalField(
        _('الطاقة (كيلو كالوري/كغم)'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    fiber_percentage = models.DecimalField(
        _('نسبة الألياف %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )
    fat_percentage = models.DecimalField(
        _('نسبة الدهون %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )
    ash_percentage = models.DecimalField(
        _('نسبة الرماد %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )
    moisture_percentage = models.DecimalField(
        _('نسبة الرطوبة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )

    # معلومات التكلفة
    cost_per_kg = models.DecimalField(
        _('التكلفة لكل كيلوغرام (دينار أردني)'),
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )

    # معلومات المخزون
    current_stock = models.DecimalField(
        _('المخزون الحالي (كغم)'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    minimum_stock = models.DecimalField(
        _('الحد الأدنى للمخزون (كغم)'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )

    # معلومات إضافية
    supplier = models.CharField(_('المورد'), max_length=200, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مكون')
        verbose_name_plural = _('المكونات')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def is_low_stock(self):
        """تحقق من انخفاض المخزون"""
        return self.current_stock <= self.minimum_stock

    @property
    def stock_status(self):
        """حالة المخزون"""
        if self.current_stock <= 0:
            return _('نفد المخزون')
        elif self.is_low_stock:
            return _('مخزون منخفض')
        else:
            return _('متوفر')
