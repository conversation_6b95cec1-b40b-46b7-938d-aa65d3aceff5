{% extends 'base.html' %}

{% block title %}خلطات الأعلاف - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">خلطات الأعلاف</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-blender"></i>
                إدارة خلطات الأعلاف
            </h1>
            <div>
                <a href="{% url 'mixtures:add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة خلطة جديدة
                </a>
                <a href="{% url 'mixtures:types' %}" class="btn btn-outline-primary">
                    <i class="fas fa-tags"></i>
                    إدارة أنواع الأعلاف
                </a>
            </div>
        </div>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-search"></i>
                    البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                {% if form %}
                    {% load crispy_forms_tags %}
                    {% crispy form %}
                {% else %}
                    <p class="text-danger">خطأ في تحميل نموذج البحث</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">قائمة خلطات الأعلاف</h5>
            </div>
            <div class="card-body">
                {% if mixtures %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الخلطة</th>
                                    <th>نوع العلف</th>
                                    <th>البروتين المستهدف %</th>
                                    <th>الطاقة المستهدفة</th>
                                    <th>تكلفة الإنتاج</th>
                                    <th>سعر البيع</th>
                                    <th>هامش الربح</th>
                                    <th>حالة الصيغة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mixture in mixtures %}
                                <tr>
                                    <td>
                                        <strong>{{ mixture.name }}</strong>
                                        {% if mixture.description %}
                                            <br><small class="text-muted">{{ mixture.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ mixture.feed_type.name }}</td>
                                    <td>{{ mixture.target_protein_percentage|floatformat:1 }}%</td>
                                    <td>{{ mixture.target_energy_kcal_per_kg|floatformat:0 }} ك.كالوري/كغم</td>
                                    <td class="currency">{{ mixture.cost_per_kg|floatformat:3 }} د.أ</td>
                                    <td class="currency">{{ mixture.selling_price_per_kg|floatformat:3 }} د.أ</td>
                                    <td>
                                        <span class="{% if mixture.profit_margin_percentage > 20 %}text-success{% elif mixture.profit_margin_percentage > 10 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ mixture.profit_margin_percentage|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if mixture.is_formula_complete %}
                                            <span class="status-badge status-active">مكتملة</span>
                                        {% else %}
                                            <span class="status-badge status-pending">غير مكتملة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'mixtures:detail' mixture.pk %}"
                                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'mixtures:edit' mixture.pk %}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'mixtures:delete' mixture.pk %}"
                                               class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if page_obj.has_other_pages %}
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                عرض {{ page_obj.start_index }} - {{ page_obj.end_index }}
                                من أصل {{ page_obj.paginator.count }} خلطة
                            </small>
                        </div>
                        <nav aria-label="تصفح الخلطات">
                            <ul class="pagination pagination-sm mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-blender fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد خلطات مطابقة للبحث</h5>
                        <p class="text-muted">جرب تغيير معايير البحث أو إضافة خلطات جديدة</p>
                        <a href="{% url 'mixtures:add' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة خلطة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تأكيد الحذف
    document.querySelectorAll('a[href*="delete"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذه الخلطة؟')) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
