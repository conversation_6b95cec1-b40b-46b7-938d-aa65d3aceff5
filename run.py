#!/usr/bin/env python
"""
تشغيل خادم Django المحسن
"""
import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    print("🚀 تشغيل نظام إدارة مصنع الأعلاف المحسن")
    print("=" * 60)
    
    # التأكد من المجلد الصحيح
    if not Path('manage.py').exists():
        print("❌ ملف manage.py غير موجود!")
        print("تأكد من أنك في مجلد المشروع الصحيح")
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من Django
    try:
        import django
        print(f"✅ Django {django.get_version()} متوفر")
    except ImportError:
        print("❌ Django غير مثبت!")
        print("قم بتثبيت Django أولاً: pip install django")
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من قاعدة البيانات
    if not Path('db.sqlite3').exists():
        print("⚠️  قاعدة البيانات غير موجودة، سيتم إنشاؤها...")
        try:
            subprocess.run([sys.executable, 'manage.py', 'migrate'], check=True)
            print("✅ تم إنشاء قاعدة البيانات")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء قاعدة البيانات")
            input("اضغط Enter للخروج...")
            return
    
    print("\n🌐 بدء تشغيل الخادم...")
    print("📍 الرابط: http://127.0.0.1:8000/")
    print("🔑 لوحة الإدارة: http://127.0.0.1:8000/admin/")
    print("👤 اسم المستخدم: admin")
    print("🔐 كلمة المرور: admin123456")
    print("\n🎯 الميزات الجديدة:")
    print("   • البحث المتقدم مع اقتراحات ذكية")
    print("   • واجهة محسنة مع تأثيرات جميلة")
    print("   • أداء أسرع مع نظام كاش")
    print("   • أمان محسن وحماية متقدمة")
    print("   • اختصارات لوحة المفاتيح (Ctrl+S, Ctrl+N)")
    print("\n" + "=" * 60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    try:
        # تشغيل الخادم
        process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # انتظار قليل ثم فتح المتصفح
        time.sleep(3)
        try:
            webbrowser.open('http://127.0.0.1:8000/')
            print("🌐 تم فتح المتصفح تلقائياً")
        except:
            print("ℹ️  افتح المتصفح يدوياً على: http://127.0.0.1:8000/")
        
        # عرض مخرجات الخادم
        for line in process.stdout:
            print(line.strip())
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
        process.terminate()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
    finally:
        print("\n👋 شكراً لاستخدام نظام إدارة مصنع الأعلاف!")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
