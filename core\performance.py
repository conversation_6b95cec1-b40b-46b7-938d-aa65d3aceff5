"""
تحسينات الأداء والميزات المتقدمة
"""

from django.core.cache import cache
from django.db.models import Prefetch, F
from django.conf import settings
from functools import wraps
import time
import logging

logger = logging.getLogger(__name__)


def cache_result(timeout=300, key_prefix=''):
    """
    Decorator لتخزين نتائج الدوال في الكاش
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح فريد للكاش
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # محاولة الحصول على النتيجة من الكاش
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return result
            
            # تنفيذ الدالة وحفظ النتيجة في الكاش
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            logger.debug(f"Cache set for {cache_key}")
            
            return result
        return wrapper
    return decorator


def measure_performance(func):
    """
    Decorator لقياس أداء الدوال
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.info(f"{func.__name__} executed in {execution_time:.4f} seconds")
        
        return result
    return wrapper


class DatabaseOptimizer:
    """
    فئة لتحسين استعلامات قاعدة البيانات
    """
    
    @staticmethod
    def optimize_orders_query():
        """تحسين استعلام الطلبات"""
        from orders.models import Order
        
        return Order.objects.select_related(
            'customer'
        ).prefetch_related(
            Prefetch('items', select_related=['mixture', 'mixture__feed_type'])
        )
    
    @staticmethod
    def optimize_ingredients_query():
        """تحسين استعلام المكونات"""
        from ingredients.models import Ingredient
        
        return Ingredient.objects.select_related(
            'category', 'supplier'
        ).prefetch_related(
            'nutritional_values'
        )
    
    @staticmethod
    def optimize_mixtures_query():
        """تحسين استعلام الخلطات"""
        from mixtures.models import FeedMixture
        
        return FeedMixture.objects.select_related(
            'feed_type'
        ).prefetch_related(
            Prefetch('components', select_related=['ingredient'])
        )


class CacheManager:
    """
    إدارة الكاش للبيانات المختلفة
    """
    
    CACHE_KEYS = {
        'dashboard_stats': 'dashboard:stats',
        'ingredients_count': 'ingredients:count',
        'orders_stats': 'orders:stats',
        'production_stats': 'production:stats',
    }
    
    @classmethod
    def get_dashboard_stats(cls):
        """الحصول على إحصائيات لوحة التحكم من الكاش"""
        cache_key = cls.CACHE_KEYS['dashboard_stats']
        stats = cache.get(cache_key)
        
        if stats is None:
            stats = cls._calculate_dashboard_stats()
            cache.set(cache_key, stats, 300)  # 5 دقائق
        
        return stats
    
    @classmethod
    def _calculate_dashboard_stats(cls):
        """حساب إحصائيات لوحة التحكم"""
        from orders.models import Order
        from ingredients.models import Ingredient
        from analytics.models import ProductionBatch
        from django.db.models import Sum, Count
        
        return {
            'total_orders': Order.objects.count(),
            'total_revenue': Order.objects.aggregate(
                total=Sum('total_amount')
            )['total'] or 0,
            'total_ingredients': Ingredient.objects.filter(is_active=True).count(),
            'low_stock_count': Ingredient.objects.filter(
                current_stock__lt=F('minimum_stock'),
                is_active=True
            ).count(),
            'total_production': ProductionBatch.objects.aggregate(
                total=Sum('quantity_produced_kg')
            )['total'] or 0,
        }
    
    @classmethod
    def invalidate_dashboard_cache(cls):
        """إلغاء كاش لوحة التحكم"""
        cache.delete(cls.CACHE_KEYS['dashboard_stats'])
    
    @classmethod
    def invalidate_all_cache(cls):
        """إلغاء جميع الكاش"""
        for key in cls.CACHE_KEYS.values():
            cache.delete(key)


class SearchOptimizer:
    """
    تحسين البحث والفلترة
    """
    
    @staticmethod
    def search_ingredients(query):
        """البحث المحسن في المكونات"""
        from ingredients.models import Ingredient
        from django.db.models import Q
        
        if not query:
            return Ingredient.objects.none()
        
        # البحث في عدة حقول
        search_query = Q(name__icontains=query) | \
                      Q(description__icontains=query) | \
                      Q(category__name__icontains=query)
        
        return Ingredient.objects.filter(
            search_query, is_active=True
        ).select_related('category', 'supplier')
    
    @staticmethod
    def search_orders(query):
        """البحث المحسن في الطلبات"""
        from orders.models import Order
        from django.db.models import Q
        
        if not query:
            return Order.objects.none()
        
        search_query = Q(order_number__icontains=query) | \
                      Q(customer__name__icontains=query) | \
                      Q(customer__company_name__icontains=query)
        
        return Order.objects.filter(
            search_query
        ).select_related('customer')


class BackgroundTasks:
    """
    المهام الخلفية لتحسين الأداء
    """
    
    @staticmethod
    def cleanup_old_notifications():
        """تنظيف الإشعارات القديمة"""
        from notifications.services import NotificationService
        return NotificationService.cleanup_old_notifications()
    
    @staticmethod
    def update_stock_alerts():
        """تحديث تنبيهات المخزون"""
        from ingredients.models import Ingredient
        from notifications.services import notify_low_stock
        
        low_stock_ingredients = Ingredient.objects.filter(
            current_stock__lt=F('minimum_stock'),
            is_active=True
        )
        
        for ingredient in low_stock_ingredients:
            notify_low_stock(ingredient)
        
        return low_stock_ingredients.count()
    
    @staticmethod
    def generate_daily_reports():
        """إنشاء التقارير اليومية"""
        from datetime import date
        from analytics.models import DailyReport
        
        today = date.today()
        
        # التحقق من وجود تقرير لليوم
        if DailyReport.objects.filter(report_date=today).exists():
            return False
        
        # إنشاء التقرير
        stats = CacheManager._calculate_dashboard_stats()
        
        DailyReport.objects.create(
            report_date=today,
            total_orders=stats['total_orders'],
            total_revenue=stats['total_revenue'],
            total_production=stats['total_production']
        )
        
        return True


class APIOptimizer:
    """
    تحسين API endpoints
    """
    
    @staticmethod
    def paginate_results(queryset, page_size=20):
        """تقسيم النتائج إلى صفحات"""
        from django.core.paginator import Paginator
        
        paginator = Paginator(queryset, page_size)
        return paginator
    
    @staticmethod
    def serialize_with_cache(obj, serializer_class, timeout=300):
        """تسلسل البيانات مع الكاش"""
        cache_key = f"serialized:{obj.__class__.__name__}:{obj.pk}"
        
        serialized_data = cache.get(cache_key)
        if serialized_data is None:
            serializer = serializer_class(obj)
            serialized_data = serializer.data
            cache.set(cache_key, serialized_data, timeout)
        
        return serialized_data


# إعدادات الأداء
PERFORMANCE_SETTINGS = {
    'CACHE_TIMEOUT': 300,  # 5 دقائق
    'SEARCH_RESULTS_LIMIT': 50,
    'PAGINATION_SIZE': 20,
    'BACKGROUND_TASK_INTERVAL': 3600,  # ساعة واحدة
}


# دوال مساعدة للأداء
def get_performance_metrics():
    """الحصول على مقاييس الأداء"""
    import psutil
    from django.db import connection
    
    return {
        'cpu_percent': psutil.cpu_percent(),
        'memory_percent': psutil.virtual_memory().percent,
        'db_queries_count': len(connection.queries),
        'cache_stats': cache._cache.get_stats() if hasattr(cache._cache, 'get_stats') else {},
    }


def optimize_images():
    """تحسين الصور (إذا كانت موجودة)"""
    # يمكن إضافة تحسين الصور هنا لاحقاً
    pass


def compress_static_files():
    """ضغط الملفات الثابتة"""
    # يمكن إضافة ضغط الملفات هنا لاحقاً
    pass
