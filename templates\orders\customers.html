{% extends 'base.html' %}

{% block title %}قائمة العملاء{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item active">العملاء</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users"></i>
                إدارة العملاء
            </h1>
            <a href="{% url 'orders:customer_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                عميل جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_customers }}</h4>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.active_customers }}</h4>
                        <p class="mb-0">عملاء نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.inactive_customers }}</h4>
                        <p class="mb-0">عملاء غير نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-search"></i>
            البحث والتصفية
        </h6>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ form.search.label_tag }}
                {{ form.search }}
            </div>
            <div class="col-md-3">
                {{ form.city.label_tag }}
                {{ form.city }}
            </div>
            <div class="col-md-3">
                {{ form.is_active.label_tag }}
                {{ form.is_active }}
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
                <a href="{% url 'orders:customers' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول العملاء -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة العملاء ({{ page_obj.paginator.count }} عميل)
        </h6>
    </div>
    <div class="card-body">
        {% if customers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>الشركة</th>
                        <th>الهاتف</th>
                        <th>المدينة</th>
                        <th>حد الائتمان</th>
                        <th>الرصيد الحالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                    <tr>
                        <td>
                            <a href="{% url 'orders:customer_detail' customer.pk %}" class="text-decoration-none">
                                <strong>{{ customer.name }}</strong>
                            </a>
                        </td>
                        <td>
                            {% if customer.company_name %}
                                {{ customer.company_name|truncatechars:30 }}
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>{{ customer.phone }}</td>
                        <td>
                            {% if customer.city %}
                                {{ customer.city }}
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td class="currency">{{ customer.credit_limit|floatformat:3 }} د.أ</td>
                        <td class="currency {% if customer.current_balance < 0 %}text-danger{% elif customer.current_balance > 0 %}text-success{% endif %}">
                            {{ customer.current_balance|floatformat:3 }} د.أ
                        </td>
                        <td>
                            {% if customer.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'orders:customer_detail' customer.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'orders:customer_edit' customer.pk %}" class="btn btn-sm btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'orders:customer_delete' customer.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح العملاء">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">السابق</a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">لم يتم العثور على أي عملاء تطابق معايير البحث</p>
            <a href="{% url 'orders:customer_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة عميل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
