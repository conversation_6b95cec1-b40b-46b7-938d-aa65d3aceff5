# Generated by Django 5.2.1 on 2025-06-03 18:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('mixtures', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductionBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الدفعة')),
                ('quantity_produced_kg', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية المنتجة (كغم)')),
                ('production_date', models.DateTimeField(verbose_name='تاريخ الإنتاج')),
                ('material_cost', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='تكلفة المواد (دينار)')),
                ('labor_cost', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='تكلفة العمالة (دينار)')),
                ('overhead_cost', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='التكاليف العامة (دينار)')),
                ('total_cost', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='التكلفة الإجمالية (دينار)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('mixture', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_batches', to='mixtures.feedmixture', verbose_name='خلطة العلف')),
                ('produced_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_batches', to=settings.AUTH_USER_MODEL, verbose_name='أنتج بواسطة')),
            ],
            options={
                'verbose_name': 'دفعة إنتاج',
                'verbose_name_plural': 'دفعات الإنتاج',
                'ordering': ['-production_date'],
            },
        ),
        migrations.CreateModel(
            name='SalesReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_date', models.DateField(verbose_name='تاريخ التقرير')),
                ('total_orders', models.IntegerField(default=0, verbose_name='إجمالي الطلبات')),
                ('total_revenue', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='إجمالي الإيرادات (دينار)')),
                ('total_cost', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='إجمالي التكاليف (دينار)')),
                ('total_profit', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=15, verbose_name='إجمالي الربح (دينار)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تقرير مبيعات',
                'verbose_name_plural': 'تقارير المبيعات',
                'ordering': ['-report_date'],
                'unique_together': {('report_date',)},
            },
        ),
    ]
