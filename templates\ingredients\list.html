{% extends 'base.html' %}

{% block title %}المكونات - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">المكونات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-seedling"></i>
                إدارة المكونات
            </h1>
            <div>
                <a href="{% url 'ingredients:add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة مكون جديد
                </a>
                <a href="{% url 'ingredients:categories' %}" class="btn btn-outline-primary">
                    <i class="fas fa-tags"></i>
                    إدارة الفئات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-search"></i>
                    البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                {% load crispy_forms_tags %}
                {% crispy form %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">قائمة المكونات</h5>
            </div>
            <div class="card-body">
                {% if ingredients %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المكون</th>
                                    <th>الفئة</th>
                                    <th>البروتين %</th>
                                    <th>الطاقة (ك.كالوري/كغم)</th>
                                    <th>التكلفة (د.أ/كغم)</th>
                                    <th>المخزون الحالي</th>
                                    <th>حالة المخزون</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in ingredients %}
                                <tr>
                                    <td>
                                        <strong>{{ ingredient.name }}</strong>
                                        {% if ingredient.description %}
                                            <br><small class="text-muted">{{ ingredient.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ ingredient.category.name }}</td>
                                    <td>{{ ingredient.protein_percentage|floatformat:1 }}%</td>
                                    <td>{{ ingredient.energy_kcal_per_kg|floatformat:0 }}</td>
                                    <td class="currency">{{ ingredient.cost_per_kg|floatformat:3 }}</td>
                                    <td>{{ ingredient.current_stock|floatformat:2 }} كغم</td>
                                    <td>
                                        <span class="status-badge 
                                            {% if ingredient.current_stock <= 0 %}status-inactive
                                            {% elif ingredient.is_low_stock %}status-pending
                                            {% else %}status-active{% endif %}">
                                            {{ ingredient.stock_status }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'ingredients:detail' ingredient.pk %}" 
                                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'ingredients:edit' ingredient.pk %}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'ingredients:delete' ingredient.pk %}" 
                                               class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if page_obj.has_other_pages %}
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                عرض {{ page_obj.start_index }} - {{ page_obj.end_index }}
                                من أصل {{ page_obj.paginator.count }} مكون
                            </small>
                        </div>
                        <nav aria-label="تصفح المكونات">
                            <ul class="pagination pagination-sm mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مكونات مطابقة للبحث</h5>
                        <p class="text-muted">جرب تغيير معايير البحث أو إضافة مكونات جديدة</p>
                        <a href="{% url 'ingredients:add' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة مكون جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ ingredients|length }}</h4>
                <p class="mb-0">إجمالي المكونات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ ingredients|length }}</h4>
                <p class="mb-0">مكونات متوفرة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4>0</h4>
                <p class="mb-0">مخزون منخفض</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>0</h4>
                <p class="mb-0">نفد المخزون</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تأكيد الحذف
    document.querySelectorAll('a[href*="delete"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا المكون؟')) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
