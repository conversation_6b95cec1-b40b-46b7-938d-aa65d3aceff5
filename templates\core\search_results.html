{% extends 'base.html' %}

{% block title %}نتائج البحث{% if query %} - {{ query }}{% endif %}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">نتائج البحث</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- شريط البحث المتقدم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-enhanced">
                <div class="card-body">
                    <form method="get" class="form-enhanced">
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <label for="search-query" class="form-label">
                                    <i class="fas fa-search"></i>
                                    البحث المتقدم
                                </label>
                                <input type="text" 
                                       id="search-query" 
                                       name="q" 
                                       class="form-control" 
                                       value="{{ query }}" 
                                       placeholder="ابحث في جميع البيانات..."
                                       autocomplete="off">
                                <div id="search-suggestions" class="position-absolute w-100 bg-white border rounded shadow-sm" style="z-index: 1000; display: none;"></div>
                            </div>
                            <div class="col-md-4">
                                <label for="model-filter" class="form-label">
                                    <i class="fas fa-filter"></i>
                                    تصفية حسب النوع
                                </label>
                                <select name="model" id="model-filter" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    {% for key, config in searchable_models.items %}
                                        <option value="{{ key }}" {% if model_filter == key %}selected{% endif %}>
                                            {{ config.display_name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-enhanced w-100">
                                    <i class="fas fa-search"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج البحث -->
    {% if query %}
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0">
                        <i class="fas fa-search-plus text-primary"></i>
                        نتائج البحث عن: <span class="text-primary">"{{ query }}"</span>
                    </h4>
                    <span class="badge bg-info fs-6">{{ total_results }} نتيجة</span>
                </div>

                {% if results %}
                    <!-- النتائج -->
                    <div class="row">
                        {% for result in results %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card card-enhanced h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start mb-2">
                                            <div class="me-3">
                                                <i class="{{ result.icon }} text-primary fa-2x"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="card-title mb-1">
                                                    <a href="{{ result.url }}" class="text-decoration-none">
                                                        {{ result.title }}
                                                    </a>
                                                </h6>
                                                <small class="text-muted">{{ result.model_name }}</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-success">{{ result.relevance }}%</span>
                                            </div>
                                        </div>
                                        
                                        {% if result.description %}
                                            <p class="card-text small text-muted">
                                                {{ result.description|safe|truncatewords:15 }}
                                            </p>
                                        {% endif %}
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="{{ result.url }}" class="btn btn-sm btn-outline-primary btn-enhanced">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <small class="text-muted">
                                                <i class="fas fa-chart-line"></i>
                                                صلة: {{ result.relevance }}%
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- إحصائيات النتائج -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie text-info"></i>
                                        إحصائيات النتائج
                                    </h6>
                                    <div class="row">
                                        {% regroup results by model_name as grouped_results %}
                                        {% for group in grouped_results %}
                                            <div class="col-md-3 mb-2">
                                                <div class="d-flex justify-content-between">
                                                    <span>{{ group.grouper }}:</span>
                                                    <span class="badge bg-primary">{{ group.list|length }}</span>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                {% else %}
                    <!-- لا توجد نتائج -->
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-search-minus text-muted" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">لم نجد أي نتائج تطابق بحثك عن "<strong>{{ query }}</strong>"</p>
                        
                        <div class="mt-4">
                            <h6>اقتراحات للبحث:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-lightbulb text-warning"></i> تأكد من صحة الكتابة</li>
                                <li><i class="fas fa-lightbulb text-warning"></i> جرب كلمات مفتاحية أخرى</li>
                                <li><i class="fas fa-lightbulb text-warning"></i> استخدم كلمات أقل أو أكثر عمومية</li>
                            </ul>
                        </div>

                        {% if suggestions %}
                            <div class="mt-4">
                                <h6>اقتراحات البحث:</h6>
                                <div class="d-flex flex-wrap justify-content-center gap-2">
                                    {% for suggestion in suggestions %}
                                        <a href="?q={{ suggestion.text }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="{{ suggestion.icon }}"></i>
                                            {{ suggestion.text }}
                                        </a>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    {% else %}
        <!-- صفحة البحث الرئيسية -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-search text-primary" style="font-size: 4rem;"></i>
            </div>
            <h3>البحث المتقدم</h3>
            <p class="text-muted">ابحث في جميع بيانات النظام بسرعة ودقة</p>
            
            <div class="row mt-5">
                <div class="col-md-8 offset-md-2">
                    <div class="row">
                        {% for key, config in searchable_models.items %}
                            <div class="col-md-4 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-{{ config.icon|default:'search' }} text-primary fa-2x mb-2"></i>
                                        <h6>{{ config.display_name }}</h6>
                                        <small class="text-muted">البحث في {{ config.display_name }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// البحث المباشر مع الاقتراحات
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-query');
    const suggestionsDiv = document.getElementById('search-suggestions');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                fetchSuggestions(query);
            }, 300);
        } else {
            suggestionsDiv.style.display = 'none';
        }
    });

    function fetchSuggestions(query) {
        fetch(`{% url 'core:search_api' %}?q=${encodeURIComponent(query)}&limit=5`)
            .then(response => response.json())
            .then(data => {
                displaySuggestions(data.suggestions);
            })
            .catch(error => {
                console.error('Error fetching suggestions:', error);
            });
    }

    function displaySuggestions(suggestions) {
        if (suggestions.length === 0) {
            suggestionsDiv.style.display = 'none';
            return;
        }

        let html = '<div class="p-2">';
        suggestions.forEach(suggestion => {
            html += `
                <div class="suggestion-item p-2 cursor-pointer border-bottom" onclick="selectSuggestion('${suggestion.text}')">
                    <i class="${suggestion.icon} me-2"></i>
                    <strong>${suggestion.text}</strong>
                    <small class="text-muted ms-2">${suggestion.type}</small>
                </div>
            `;
        });
        html += '</div>';

        suggestionsDiv.innerHTML = html;
        suggestionsDiv.style.display = 'block';
    }

    // إخفاء الاقتراحات عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            suggestionsDiv.style.display = 'none';
        }
    });
});

function selectSuggestion(text) {
    document.getElementById('search-query').value = text;
    document.getElementById('search-suggestions').style.display = 'none';
    document.querySelector('form').submit();
}
</script>
{% endblock %}
