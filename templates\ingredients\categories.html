{% extends 'base.html' %}

{% block title %}فئات المكونات - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:list' %}">المكونات</a></li>
        <li class="breadcrumb-item active">فئات المكونات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-tags"></i>
                إدارة فئات المكونات
            </h1>
            <div>
                <a href="{% url 'ingredients:category_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة فئة جديدة
                </a>
                <a href="{% url 'ingredients:list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للمكونات
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">قائمة فئات المكونات</h5>
            </div>
            <div class="card-body">
                {% if categories %}
                    <div class="row">
                        {% for category in categories %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-left-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h5 class="card-title text-primary">
                                            <i class="fas fa-tag"></i>
                                            {{ category.name }}
                                        </h5>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'ingredients:category_edit' category.pk %}">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="{% url 'ingredients:category_delete' category.pk %}">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    {% if category.description %}
                                        <p class="card-text text-muted">{{ category.description }}</p>
                                    {% else %}
                                        <p class="card-text text-muted fst-italic">لا يوجد وصف</p>
                                    {% endif %}

                                    <div class="mt-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="text-muted">عدد المكونات:</span>
                                            <span class="badge bg-primary">{{ category.ingredients_count }}</span>
                                        </div>
                                    </div>

                                    {% if category.ingredients.exists %}
                                        <div class="mt-3">
                                            <small class="text-muted">أمثلة على المكونات:</small>
                                            <div class="mt-1">
                                                {% for ingredient in category.ingredients.all|slice:":3" %}
                                                    <span class="badge bg-light text-dark me-1">{{ ingredient.name }}</span>
                                                {% endfor %}
                                                {% if category.ingredients_count > 3 %}
                                                    <span class="badge bg-secondary">+{{ category.ingredients_count|add:"-3" }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="card-footer bg-transparent">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        تم الإنشاء: {{ category.created_at|date:"d/m/Y" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد فئات مكونات</h5>
                        <p class="text-muted">ابدأ بإضافة فئات لتنظيم مكونات الأعلاف</p>
                        <a href="{% url 'ingredients:category_add' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة فئة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if categories %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ categories.count }}</h4>
                        <span>إجمالي الفئات</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_ingredients }}</h4>
                        <span>إجمالي المكونات</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-seedling fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ avg_ingredients_per_category|floatformat:1 }}</h4>
                        <span>متوسط المكونات/فئة</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ largest_category.ingredients_count }}</h4>
                        <span>أكبر فئة</span>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-crown fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تأكيد الحذف
    document.querySelectorAll('a[href*="delete"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع المكونات المرتبطة بها أيضاً.')) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
