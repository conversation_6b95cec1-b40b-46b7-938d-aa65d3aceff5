#!/usr/bin/env python
"""
إضافة بيانات تجريبية لخلطات الأعلاف
"""

import os
import sys
import django
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
django.setup()

from mixtures.models import FeedType, FeedMixture, MixtureComponent
from ingredients.models import Ingredient, IngredientCategory


def create_feed_types():
    """إنشاء أنواع الأعلاف"""
    feed_types_data = [
        {
            'name': 'علف دجاج بياض',
            'target_animal': 'دجاج بياض',
            'description': 'علف مخصص للدجاج البياض لزيادة إنتاج البيض'
        },
        {
            'name': 'علف دجاج لاحم',
            'target_animal': 'دجاج لاحم',
            'description': 'علف مخصص للدجاج اللاحم لزيادة الوزن'
        },
        {
            'name': 'علف أبقار حلوب',
            'target_animal': 'أبقار حلوب',
            'description': 'علف مخصص للأبقار الحلوب لزيادة إنتاج الحليب'
        },
        {
            'name': 'علف أغنام',
            'target_animal': 'أغنام',
            'description': 'علف مخصص للأغنام والماعز'
        },
        {
            'name': 'علف أسماك',
            'target_animal': 'أسماك',
            'description': 'علف مخصص لتربية الأسماك'
        }
    ]
    
    created_types = []
    for data in feed_types_data:
        feed_type, created = FeedType.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"✓ تم إنشاء نوع العلف: {feed_type.name}")
        else:
            print(f"- نوع العلف موجود بالفعل: {feed_type.name}")
        created_types.append(feed_type)
    
    return created_types


def create_mixtures(feed_types):
    """إنشاء خلطات الأعلاف"""
    mixtures_data = [
        {
            'name': 'خلطة دجاج بياض عالية البروتين',
            'feed_type': feed_types[0],  # دجاج بياض
            'description': 'خلطة غنية بالبروتين لزيادة إنتاج البيض',
            'target_protein_percentage': Decimal('18.0'),
            'target_energy_kcal_per_kg': Decimal('2800'),
            'target_fiber_percentage': Decimal('4.0'),
            'selling_price_per_kg': Decimal('0.850'),
            'batch_size_kg': Decimal('1000'),
            'notes': 'مناسبة للدجاج البياض في فترة الذروة'
        },
        {
            'name': 'خلطة دجاج لاحم نمو سريع',
            'feed_type': feed_types[1],  # دجاج لاحم
            'description': 'خلطة عالية الطاقة للنمو السريع',
            'target_protein_percentage': Decimal('22.0'),
            'target_energy_kcal_per_kg': Decimal('3100'),
            'target_fiber_percentage': Decimal('3.5'),
            'selling_price_per_kg': Decimal('0.920'),
            'batch_size_kg': Decimal('2000'),
            'notes': 'للدجاج اللاحم من عمر 3-6 أسابيع'
        },
        {
            'name': 'خلطة أبقار حلوب متوازنة',
            'feed_type': feed_types[2],  # أبقار حلوب
            'description': 'خلطة متوازنة لإنتاج الحليب',
            'target_protein_percentage': Decimal('16.0'),
            'target_energy_kcal_per_kg': Decimal('2600'),
            'target_fiber_percentage': Decimal('18.0'),
            'selling_price_per_kg': Decimal('0.650'),
            'batch_size_kg': Decimal('5000'),
            'notes': 'مناسبة للأبقار الحلوب عالية الإنتاج'
        },
        {
            'name': 'خلطة أغنام اقتصادية',
            'feed_type': feed_types[3],  # أغنام
            'description': 'خلطة اقتصادية للأغنام',
            'target_protein_percentage': Decimal('14.0'),
            'target_energy_kcal_per_kg': Decimal('2400'),
            'target_fiber_percentage': Decimal('12.0'),
            'selling_price_per_kg': Decimal('0.580'),
            'batch_size_kg': Decimal('1500'),
            'notes': 'مناسبة للأغنام في فترة الحمل'
        }
    ]
    
    created_mixtures = []
    for data in mixtures_data:
        mixture, created = FeedMixture.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"✓ تم إنشاء الخلطة: {mixture.name}")
        else:
            print(f"- الخلطة موجودة بالفعل: {mixture.name}")
        created_mixtures.append(mixture)
    
    return created_mixtures


def create_mixture_components(mixtures):
    """إنشاء مكونات الخلطات"""
    
    # الحصول على المكونات المتاحة
    try:
        corn = Ingredient.objects.filter(name__icontains='ذرة').first()
        wheat = Ingredient.objects.filter(name__icontains='قمح').first()
        soybean = Ingredient.objects.filter(name__icontains='كسب الصويا').first()
        barley = Ingredient.objects.filter(name__icontains='شعير').first()
        wheat_bran = Ingredient.objects.filter(name__icontains='نخالة القمح').first()

        if not all([corn, wheat, soybean, barley, wheat_bran]):
            print("⚠️  لم يتم العثور على بعض المكونات الأساسية. تأكد من تشغيل add_sample_data.py أولاً")
            print("المكونات المتاحة:")
            for ingredient in Ingredient.objects.all()[:10]:
                print(f"  - {ingredient.name}")
            return
    except Exception as e:
        print(f"❌ خطأ في الحصول على المكونات: {e}")
        return
    
    # مكونات خلطة دجاج بياض
    components_data = [
        # خلطة دجاج بياض عالية البروتين
        {
            'mixture': mixtures[0],
            'components': [
                {'ingredient': corn, 'percentage': Decimal('55.0'), 'notes': 'مصدر رئيسي للطاقة'},
                {'ingredient': soybean, 'percentage': Decimal('25.0'), 'notes': 'مصدر البروتين الرئيسي'},
                {'ingredient': wheat, 'percentage': Decimal('15.0'), 'notes': 'مصدر إضافي للطاقة'},
                {'ingredient': wheat_bran, 'percentage': Decimal('5.0'), 'notes': 'مصدر الفيتامينات والمعادن'},
            ]
        },
        # خلطة دجاج لاحم نمو سريع
        {
            'mixture': mixtures[1],
            'components': [
                {'ingredient': corn, 'percentage': Decimal('60.0'), 'notes': 'طاقة عالية للنمو'},
                {'ingredient': soybean, 'percentage': Decimal('30.0'), 'notes': 'بروتين عالي الجودة'},
                {'ingredient': wheat, 'percentage': Decimal('8.0'), 'notes': 'طاقة إضافية'},
                {'ingredient': wheat_bran, 'percentage': Decimal('2.0'), 'notes': 'فيتامينات ومعادن'},
            ]
        },
        # خلطة أبقار حلوب متوازنة
        {
            'mixture': mixtures[2],
            'components': [
                {'ingredient': corn, 'percentage': Decimal('35.0'), 'notes': 'مصدر الطاقة'},
                {'ingredient': barley, 'percentage': Decimal('25.0'), 'notes': 'ألياف قابلة للهضم'},
                {'ingredient': soybean, 'percentage': Decimal('20.0'), 'notes': 'بروتين عالي الجودة'},
                {'ingredient': wheat, 'percentage': Decimal('15.0'), 'notes': 'طاقة إضافية'},
                {'ingredient': wheat_bran, 'percentage': Decimal('5.0'), 'notes': 'فيتامينات ومعادن'},
            ]
        },
        # خلطة أغنام اقتصادية
        {
            'mixture': mixtures[3],
            'components': [
                {'ingredient': barley, 'percentage': Decimal('40.0'), 'notes': 'مصدر رئيسي اقتصادي'},
                {'ingredient': corn, 'percentage': Decimal('30.0'), 'notes': 'طاقة'},
                {'ingredient': wheat, 'percentage': Decimal('20.0'), 'notes': 'طاقة إضافية'},
                {'ingredient': wheat_bran, 'percentage': Decimal('10.0'), 'notes': 'ألياف وفيتامينات'},
            ]
        }
    ]
    
    for mixture_data in components_data:
        mixture = mixture_data['mixture']
        print(f"\n📝 إضافة مكونات للخلطة: {mixture.name}")
        
        for comp_data in mixture_data['components']:
            component, created = MixtureComponent.objects.get_or_create(
                mixture=mixture,
                ingredient=comp_data['ingredient'],
                defaults={
                    'percentage': comp_data['percentage'],
                    'notes': comp_data['notes']
                }
            )
            if created:
                print(f"  ✓ تم إضافة: {comp_data['ingredient'].name} ({comp_data['percentage']}%)")
            else:
                print(f"  - موجود: {comp_data['ingredient'].name}")
        
        # حساب التكلفة والقيم الغذائية
        mixture.calculate_cost()
        nutritional_values = mixture.calculate_nutritional_values()
        print(f"  💰 تكلفة الإنتاج: {mixture.cost_per_kg} د.ع/كغم")
        print(f"  📊 البروتين الفعلي: {nutritional_values['protein_percentage']}%")
        print(f"  ⚡ الطاقة الفعلية: {nutritional_values['energy_kcal_per_kg']} ك.كالوري/كغم")


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إضافة بيانات خلطات الأعلاف...")
    print("=" * 50)
    
    # إنشاء أنواع الأعلاف
    print("\n📋 إنشاء أنواع الأعلاف...")
    feed_types = create_feed_types()
    
    # إنشاء الخلطات
    print("\n🧪 إنشاء خلطات الأعلاف...")
    mixtures = create_mixtures(feed_types)
    
    # إنشاء مكونات الخلطات
    print("\n🔧 إنشاء مكونات الخلطات...")
    create_mixture_components(mixtures)
    
    print("\n" + "=" * 50)
    print("✅ تم الانتهاء من إضافة بيانات خلطات الأعلاف بنجاح!")
    print(f"📊 تم إنشاء {len(feed_types)} أنواع أعلاف و {len(mixtures)} خلطات")
    print("\n🌐 يمكنك الآن زيارة: http://localhost:8000/mixtures/")


if __name__ == '__main__':
    main()
