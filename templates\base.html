{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}مصنع الأعلاف{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مصنع الأعلاف">
    <meta name="msapplication-TileColor" content="#007bff">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% static 'manifest.json' %}">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .stats-card .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .currency {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="{% url 'core:dashboard' %}">
                <i class="fas fa-industry"></i>
                مصنع الأعلاف
            </a>

            <!-- شريط البحث -->
            <div class="flex-grow-1 mx-4">
                <form class="d-flex" action="{% url 'core:search' %}" method="get">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" placeholder="البحث في جميع البيانات..."
                               value="{{ request.GET.q }}" id="global-search" autocomplete="off">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <!-- نتائج البحث المقترحة -->
                    <div id="search-suggestions" class="position-absolute bg-white border rounded shadow-sm mt-5 w-100" style="display: none; z-index: 1000;">
                    </div>
                </form>
            </div>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                        {{ user.username }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'admin:index' %}">لوحة الإدارة</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'admin:logout' %}">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-lg-2 d-lg-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'core:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'ingredients' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'ingredients:list' %}">
                                <i class="fas fa-seedling"></i>
                                المكونات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'mixtures' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'mixtures:list' %}">
                                <i class="fas fa-blender"></i>
                                خلطات الأعلاف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'orders' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'orders:list' %}">
                                <i class="fas fa-shopping-cart"></i>
                                الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'customers' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'orders:customers' %}">
                                <i class="fas fa-users"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'analytics' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'analytics:dashboard' %}">
                                <i class="fas fa-chart-bar"></i>
                                التحليلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'invoices' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'invoices:list' %}">
                                <i class="fas fa-file-invoice"></i>
                                الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'admin:index' %}">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>

                    <!-- Notifications and Install App Button -->
                    <div class="d-flex align-items-center">
                        <!-- Notifications Dropdown -->
                        <div class="dropdown me-3">
                            <button class="btn btn-outline-light position-relative" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                {% if unread_notifications_count > 0 %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {{ unread_notifications_count }}
                                </span>
                                {% endif %}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end notifications-dropdown" style="width: 350px;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>الإشعارات</span>
                                    {% if unread_notifications_count > 0 %}
                                    <a href="#" class="btn btn-sm btn-link text-decoration-none" onclick="markAllAsRead()">
                                        تحديد الكل كمقروء
                                    </a>
                                    {% endif %}
                                </li>
                                <li><hr class="dropdown-divider"></li>

                                {% if unread_notifications %}
                                    {% for notification in unread_notifications %}
                                    <li>
                                        <a class="dropdown-item notification-item" href="#" onclick="markAsRead({{ notification.pk }})">
                                            <div class="d-flex">
                                                <div class="notification-icon me-2">
                                                    {% if notification.notification_type == 'order' %}
                                                        <i class="fas fa-shopping-cart text-primary"></i>
                                                    {% elif notification.notification_type == 'payment' %}
                                                        <i class="fas fa-dollar-sign text-success"></i>
                                                    {% elif notification.notification_type == 'stock' %}
                                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                                    {% elif notification.notification_type == 'invoice' %}
                                                        <i class="fas fa-file-invoice text-info"></i>
                                                    {% else %}
                                                        <i class="fas fa-info-circle text-secondary"></i>
                                                    {% endif %}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="notification-title">{{ notification.title }}</div>
                                                    <div class="notification-message text-muted small">{{ notification.message|truncatechars:50 }}</div>
                                                    <div class="notification-time text-muted small">{{ notification.time_since_created }}</div>
                                                </div>
                                            </div>
                                        </a>
                                    </li>
                                    {% endfor %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-center" href="/notifications/">
                                            عرض جميع الإشعارات
                                        </a>
                                    </li>
                                {% else %}
                                    <li>
                                        <div class="dropdown-item text-center text-muted">
                                            <i class="fas fa-bell-slash fa-2x mb-2"></i>
                                            <p>لا توجد إشعارات جديدة</p>
                                        </div>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>

                        <!-- Install App Button -->
                        <button id="install-button" class="btn btn-outline-light btn-sm" style="display: none;">
                            <i class="fas fa-download"></i>
                            تثبيت التطبيق
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-lg-10 ms-sm-auto main-content">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% block breadcrumb %}{% endblock %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- PWA JavaScript -->
    <script src="{% static 'js/pwa.js' %}"></script>

    <!-- Notifications JavaScript -->
    <script>
        function markAsRead(notificationId) {
            fetch(`/notifications/${notificationId}/mark-read/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث عداد الإشعارات
                    updateNotificationCount();
                    // إخفاء الإشعار أو تحديث مظهره
                    location.reload(); // يمكن تحسين هذا لاحقاً
                }
            })
            .catch(error => console.error('Error:', error));
        }

        function markAllAsRead() {
            fetch('/notifications/mark-all-read/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => console.error('Error:', error));
        }

        function updateNotificationCount() {
            fetch('/notifications/count/')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('.badge.bg-danger');
                if (data.count > 0) {
                    if (badge) {
                        badge.textContent = data.count;
                    }
                } else {
                    if (badge) {
                        badge.style.display = 'none';
                    }
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // تحديث الإشعارات كل 30 ثانية
        setInterval(updateNotificationCount, 30000);
    </script>

    <!-- Search JavaScript -->
    <script>
        let searchTimeout;
        const searchInput = document.getElementById('global-search');
        const suggestionsDiv = document.getElementById('search-suggestions');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    suggestionsDiv.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    fetchSuggestions(query);
                }, 300);
            });

            // إخفاء الاقتراحات عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
                    suggestionsDiv.style.display = 'none';
                }
            });
        }

        function fetchSuggestions(query) {
            fetch(`/dashboard/search/suggestions/?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySuggestions(data.suggestions);
                })
                .catch(error => {
                    console.error('Error fetching suggestions:', error);
                });
        }

        function displaySuggestions(suggestions) {
            if (suggestions.length === 0) {
                suggestionsDiv.style.display = 'none';
                return;
            }

            let html = '<div class="p-2">';
            suggestions.forEach(suggestion => {
                const icon = getTypeIcon(suggestion.type);
                html += `
                    <div class="suggestion-item p-2 border-bottom cursor-pointer" onclick="selectSuggestion('${suggestion.text}')">
                        <i class="${icon} me-2"></i>
                        ${suggestion.text}
                        <small class="text-muted ms-2">(${getTypeLabel(suggestion.type)})</small>
                    </div>
                `;
            });
            html += '</div>';

            suggestionsDiv.innerHTML = html;
            suggestionsDiv.style.display = 'block';
        }

        function selectSuggestion(text) {
            searchInput.value = text;
            suggestionsDiv.style.display = 'none';
            searchInput.form.submit();
        }

        function getTypeIcon(type) {
            const icons = {
                'ingredient': 'fas fa-seedling text-success',
                'mixture': 'fas fa-blender text-primary',
                'customer': 'fas fa-user text-info',
                'order': 'fas fa-shopping-cart text-warning'
            };
            return icons[type] || 'fas fa-search';
        }

        function getTypeLabel(type) {
            const labels = {
                'ingredient': 'مكون',
                'mixture': 'خلطة',
                'customer': 'عميل',
                'order': 'طلب'
            };
            return labels[type] || 'عام';
        }
    </script>

    <style>
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        #search-suggestions {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
