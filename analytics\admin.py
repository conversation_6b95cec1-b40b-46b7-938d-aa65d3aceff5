from django.contrib import admin
from .models import ProductionBatch, SalesReport


@admin.register(ProductionBatch)
class ProductionBatchAdmin(admin.ModelAdmin):
    list_display = [
        'batch_number', 'mixture', 'quantity_produced_kg', 'production_date',
        'produced_by', 'total_cost', 'cost_per_kg'
    ]
    list_filter = ['production_date', 'mixture__feed_type', 'produced_by']
    search_fields = ['batch_number', 'mixture__name']

    fieldsets = (
        ('معلومات الدفعة', {
            'fields': ('batch_number', 'mixture', 'quantity_produced_kg', 'production_date', 'produced_by')
        }),
        ('تكاليف الإنتاج', {
            'fields': ('material_cost', 'labor_cost', 'overhead_cost', 'total_cost')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ['batch_number', 'total_cost']

    def cost_per_kg(self, obj):
        return f"{obj.cost_per_kg:.3f} د.ع"
    cost_per_kg.short_description = 'التكلفة/كغم'

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كانت دفعة جديدة
            obj.produced_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SalesReport)
class SalesReportAdmin(admin.ModelAdmin):
    list_display = [
        'report_date', 'total_orders', 'total_revenue',
        'total_cost', 'total_profit', 'profit_margin'
    ]
    list_filter = ['report_date']
    search_fields = ['report_date']

    fieldsets = (
        ('معلومات التقرير', {
            'fields': ('report_date', 'total_orders')
        }),
        ('المبالغ المالية', {
            'fields': ('total_revenue', 'total_cost', 'total_profit')
        }),
    )

    readonly_fields = ['total_profit']

    def profit_margin(self, obj):
        if obj.total_revenue > 0:
            margin = (obj.total_profit / obj.total_revenue) * 100
            return f"{margin:.2f}%"
        return "0%"
    profit_margin.short_description = 'هامش الربح'
