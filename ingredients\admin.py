from django.contrib import admin
from .models import IngredientCategory, Ingredient


@admin.register(IngredientCategory)
class IngredientCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name', 'description']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(Ingredient)
class IngredientAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'category', 'protein_percentage', 'energy_kcal_per_kg',
        'cost_per_kg', 'current_stock', 'stock_status', 'is_active'
    ]
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'supplier']
    list_editable = ['cost_per_kg', 'current_stock', 'is_active']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'category', 'description', 'is_active')
        }),
        ('القيم الغذائية', {
            'fields': (
                'protein_percentage', 'energy_kcal_per_kg', 'fiber_percentage',
                'fat_percentage', 'ash_percentage', 'moisture_percentage'
            )
        }),
        ('معلومات التكلفة والمخزون', {
            'fields': ('cost_per_kg', 'current_stock', 'minimum_stock')
        }),
        ('معلومات إضافية', {
            'fields': ('supplier', 'notes')
        }),
    )

    def stock_status(self, obj):
        return obj.stock_status
    stock_status.short_description = 'حالة المخزون'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category')
