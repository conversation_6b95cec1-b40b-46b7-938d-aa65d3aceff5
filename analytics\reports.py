"""
نظام التقارير المتقدمة
"""

from django.db.models import Sum, Count, Avg, F, Q, Min, Max
from django.utils import timezone
from datetime import datetime, timedelta
from orders.models import Order, Customer
from ingredients.models import Ingredient
from mixtures.models import FeedMixture
from invoices.models import Invoice, Payment
from analytics.models import ProductionBatch
import json


class ReportGenerator:
    """مولد التقارير المتقدمة"""
    
    def __init__(self, start_date=None, end_date=None, filters=None):
        self.start_date = start_date or (timezone.now().date() - timedelta(days=30))
        self.end_date = end_date or timezone.now().date()
        self.filters = filters or {}
    
    def generate_comprehensive_report(self):
        """تقرير شامل لجميع العمليات"""
        return {
            'period': {
                'start_date': self.start_date,
                'end_date': self.end_date,
                'days_count': (self.end_date - self.start_date).days + 1
            },
            'sales_summary': self.get_sales_summary(),
            'production_summary': self.get_production_summary(),
            'inventory_summary': self.get_inventory_summary(),
            'financial_summary': self.get_financial_summary(),
            'customer_analysis': self.get_customer_analysis(),
            'product_performance': self.get_product_performance(),
            'trends': self.get_trends_analysis(),
        }
    
    def get_sales_summary(self):
        """ملخص المبيعات"""
        orders = Order.objects.filter(
            order_date__range=[self.start_date, self.end_date]
        )
        
        if self.filters.get('customer'):
            orders = orders.filter(customer_id=self.filters['customer'])
        
        total_orders = orders.count()
        total_revenue = orders.aggregate(total=Sum('total_amount'))['total'] or 0
        avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
        
        # تحليل حسب الحالة
        status_breakdown = orders.values('status').annotate(
            count=Count('id'),
            revenue=Sum('total_amount')
        )
        
        # أفضل أيام المبيعات
        daily_sales = orders.extra(
            select={'day': 'DATE(order_date)'}
        ).values('day').annotate(
            orders_count=Count('id'),
            revenue=Sum('total_amount')
        ).order_by('-revenue')[:7]
        
        return {
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'avg_order_value': avg_order_value,
            'status_breakdown': list(status_breakdown),
            'daily_sales': list(daily_sales),
            'growth_rate': self.calculate_growth_rate('sales')
        }
    
    def get_production_summary(self):
        """ملخص الإنتاج"""
        batches = ProductionBatch.objects.filter(
            production_date__range=[self.start_date, self.end_date]
        )
        
        total_batches = batches.count()
        total_production = batches.aggregate(total=Sum('quantity_produced_kg'))['total'] or 0
        total_cost = batches.aggregate(total=Sum('total_cost'))['total'] or 0
        avg_batch_size = total_production / total_batches if total_batches > 0 else 0
        
        # تحليل حسب نوع الخلطة
        mixture_breakdown = batches.values(
            'mixture__name', 'mixture__feed_type__name'
        ).annotate(
            batches_count=Count('id'),
            total_produced=Sum('quantity_produced_kg'),
            total_cost=Sum('total_cost')
        ).order_by('-total_produced')
        
        # كفاءة الإنتاج
        efficiency_data = batches.aggregate(
            avg_efficiency=Avg('efficiency_percentage'),
            min_efficiency=Min('efficiency_percentage'),
            max_efficiency=Max('efficiency_percentage')
        )
        
        return {
            'total_batches': total_batches,
            'total_production': total_production,
            'total_cost': total_cost,
            'avg_batch_size': avg_batch_size,
            'cost_per_kg': total_cost / total_production if total_production > 0 else 0,
            'mixture_breakdown': list(mixture_breakdown),
            'efficiency': efficiency_data,
            'growth_rate': self.calculate_growth_rate('production')
        }
    
    def get_inventory_summary(self):
        """ملخص المخزون"""
        ingredients = Ingredient.objects.filter(is_active=True)
        
        total_ingredients = ingredients.count()
        total_stock_value = ingredients.aggregate(
            total_value=Sum(F('current_stock') * F('cost_per_kg'))
        )['total_value'] or 0
        
        # المكونات منخفضة المخزون
        low_stock = ingredients.filter(
            current_stock__lt=F('minimum_stock')
        ).count()
        
        # المكونات المضافة حديثاً (آخر 30 يوم)
        recent_ingredients = ingredients.filter(
            created_at__gte=timezone.now().date() - timedelta(days=30)
        ).count()
        
        # أكثر المكونات استهلاكاً
        # هذا يتطلب تتبع الاستهلاك في دفعات الإنتاج
        top_consumed = ingredients.annotate(
            consumption=Sum('mixturecomponent__percentage') * Sum('mixturecomponent__mixture__productionbatch__quantity_produced_kg') / 100
        ).order_by('-consumption')[:10]
        
        return {
            'total_ingredients': total_ingredients,
            'total_stock_value': total_stock_value,
            'low_stock_count': low_stock,
            'recent_ingredients_count': recent_ingredients,
            'avg_stock_value': total_stock_value / total_ingredients if total_ingredients > 0 else 0,
            'top_consumed': [
                {
                    'name': ing.name,
                    'consumption': ing.consumption or 0,
                    'current_stock': ing.current_stock,
                    'value': ing.current_stock * ing.cost_per_kg
                }
                for ing in top_consumed
            ]
        }
    
    def get_financial_summary(self):
        """الملخص المالي"""
        # الإيرادات من الطلبات
        orders_revenue = Order.objects.filter(
            order_date__range=[self.start_date, self.end_date]
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        # المدفوعات المستلمة
        payments_received = Payment.objects.filter(
            payment_date__range=[self.start_date, self.end_date]
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # تكاليف الإنتاج
        production_costs = ProductionBatch.objects.filter(
            production_date__range=[self.start_date, self.end_date]
        ).aggregate(total=Sum('total_cost'))['total'] or 0
        
        # الفواتير المعلقة
        outstanding_invoices = Invoice.objects.filter(
            status__in=['sent', 'overdue']
        ).aggregate(
            total=Sum(F('total_amount') - F('paid_amount'))
        )['total'] or 0
        
        # هامش الربح
        gross_profit = orders_revenue - production_costs
        profit_margin = (gross_profit / orders_revenue * 100) if orders_revenue > 0 else 0
        
        return {
            'orders_revenue': orders_revenue,
            'payments_received': payments_received,
            'production_costs': production_costs,
            'outstanding_invoices': outstanding_invoices,
            'gross_profit': gross_profit,
            'profit_margin': profit_margin,
            'cash_flow': payments_received - production_costs
        }
    
    def get_customer_analysis(self):
        """تحليل العملاء"""
        customers = Customer.objects.filter(is_active=True).annotate(
            orders_count=Count('orders', filter=Q(orders__order_date__range=[self.start_date, self.end_date])),
            total_spent=Sum('orders__total_amount', filter=Q(orders__order_date__range=[self.start_date, self.end_date])),
            avg_order_value=Avg('orders__total_amount', filter=Q(orders__order_date__range=[self.start_date, self.end_date]))
        ).filter(orders_count__gt=0)
        
        # أفضل العملاء
        top_customers = customers.order_by('-total_spent')[:10]
        
        # العملاء الجدد
        new_customers = Customer.objects.filter(
            created_at__range=[self.start_date, self.end_date]
        ).count()
        
        # معدل تكرار الطلبات
        repeat_customers = customers.filter(orders_count__gt=1).count()
        total_active_customers = customers.count()
        repeat_rate = (repeat_customers / total_active_customers * 100) if total_active_customers > 0 else 0
        
        return {
            'total_active_customers': total_active_customers,
            'new_customers': new_customers,
            'repeat_customers': repeat_customers,
            'repeat_rate': repeat_rate,
            'top_customers': [
                {
                    'name': customer.name,
                    'orders_count': customer.orders_count,
                    'total_spent': customer.total_spent or 0,
                    'avg_order_value': customer.avg_order_value or 0
                }
                for customer in top_customers
            ]
        }
    
    def get_product_performance(self):
        """أداء المنتجات"""
        # أداء الخلطات من خلال الطلبات
        mixture_performance = FeedMixture.objects.annotate(
            orders_count=Count('orderitem__order', filter=Q(orderitem__order__order_date__range=[self.start_date, self.end_date])),
            total_sold=Sum('orderitem__quantity_kg', filter=Q(orderitem__order__order_date__range=[self.start_date, self.end_date])),
            revenue=Sum(F('orderitem__quantity_kg') * F('orderitem__price_per_kg'), filter=Q(orderitem__order__order_date__range=[self.start_date, self.end_date]))
        ).filter(total_sold__gt=0).order_by('-revenue')
        
        # أفضل المنتجات
        top_products = mixture_performance[:10]
        
        return {
            'top_products': [
                {
                    'name': mixture.name,
                    'feed_type': mixture.feed_type.name,
                    'orders_count': mixture.orders_count,
                    'total_sold': mixture.total_sold or 0,
                    'revenue': mixture.revenue or 0,
                    'avg_price': (mixture.revenue / mixture.total_sold) if mixture.total_sold > 0 else 0
                }
                for mixture in top_products
            ]
        }
    
    def get_trends_analysis(self):
        """تحليل الاتجاهات"""
        # اتجاه المبيعات الأسبوعي
        weekly_sales = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            week_end = min(current_date + timedelta(days=6), self.end_date)
            week_revenue = Order.objects.filter(
                order_date__range=[current_date, week_end]
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            weekly_sales.append({
                'week_start': current_date,
                'week_end': week_end,
                'revenue': week_revenue
            })
            
            current_date = week_end + timedelta(days=1)
        
        return {
            'weekly_sales': weekly_sales,
            'sales_trend': self.calculate_trend(weekly_sales, 'revenue')
        }
    
    def calculate_growth_rate(self, metric_type):
        """حساب معدل النمو"""
        # مقارنة مع الفترة السابقة
        period_length = (self.end_date - self.start_date).days + 1
        previous_start = self.start_date - timedelta(days=period_length)
        previous_end = self.start_date - timedelta(days=1)
        
        if metric_type == 'sales':
            current_value = Order.objects.filter(
                order_date__range=[self.start_date, self.end_date]
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            previous_value = Order.objects.filter(
                order_date__range=[previous_start, previous_end]
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
        elif metric_type == 'production':
            current_value = ProductionBatch.objects.filter(
                production_date__range=[self.start_date, self.end_date]
            ).aggregate(total=Sum('quantity_produced_kg'))['total'] or 0
            
            previous_value = ProductionBatch.objects.filter(
                production_date__range=[previous_start, previous_end]
            ).aggregate(total=Sum('quantity_produced_kg'))['total'] or 0
        
        else:
            return 0
        
        if previous_value > 0:
            return ((current_value - previous_value) / previous_value) * 100
        else:
            return 0 if current_value == 0 else 100
    
    def calculate_trend(self, data_points, value_key):
        """حساب اتجاه البيانات"""
        if len(data_points) < 2:
            return 'stable'
        
        values = [point[value_key] for point in data_points]
        
        # حساب الاتجاه العام
        increases = 0
        decreases = 0
        
        for i in range(1, len(values)):
            if values[i] > values[i-1]:
                increases += 1
            elif values[i] < values[i-1]:
                decreases += 1
        
        if increases > decreases:
            return 'increasing'
        elif decreases > increases:
            return 'decreasing'
        else:
            return 'stable'
