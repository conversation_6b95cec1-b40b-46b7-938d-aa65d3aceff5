{% extends 'base.html' %}

{% block title %}الفواتير - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">الفواتير</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-file-invoice text-primary"></i>
                إدارة الفواتير
            </h1>
            <div>
                <a href="{% url 'orders:list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-plus"></i>
                    إنشاء فاتورة من طلب
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الفواتير
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي المبلغ
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_amount|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            المبلغ المدفوع
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.paid_amount|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            المبلغ المعلق
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.pending_amount|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="البحث في رقم الفاتورة أو اسم العميل" value="{{ request.GET.search }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for status, label in status_choices %}
                                <option value="{{ status }}" {% if request.GET.status == status %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'invoices:list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">قائمة الفواتير</h5>
            </div>
            <div class="card-body">
                {% if invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <strong>{{ invoice.invoice_number }}</strong>
                                    </td>
                                    <td>{{ invoice.customer.name }}</td>
                                    <td>{{ invoice.issue_date|date:"d/m/Y" }}</td>
                                    <td>
                                        {{ invoice.due_date|date:"d/m/Y" }}
                                        {% if invoice.is_overdue %}
                                            <span class="badge bg-danger ms-1">متأخرة</span>
                                        {% endif %}
                                    </td>
                                    <td class="currency">{{ invoice.total_amount|floatformat:2 }} د.أ</td>
                                    <td class="currency">{{ invoice.paid_amount|floatformat:2 }} د.أ</td>
                                    <td class="currency">{{ invoice.remaining_amount|floatformat:2 }} د.أ</td>
                                    <td>
                                        {% if invoice.status == 'draft' %}
                                            <span class="badge bg-secondary">مسودة</span>
                                        {% elif invoice.status == 'sent' %}
                                            <span class="badge bg-primary">مرسلة</span>
                                        {% elif invoice.status == 'paid' %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% elif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">متأخرة</span>
                                        {% elif invoice.status == 'cancelled' %}
                                            <span class="badge bg-dark">ملغية</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'invoices:detail' invoice.pk %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'invoices:pdf' invoice.pk %}" class="btn btn-outline-danger" title="تحميل PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد فواتير</h5>
                        <p class="text-muted">ابدأ بإنشاء فواتير من الطلبات المكتملة</p>
                        <a href="{% url 'orders:list' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            عرض الطلبات
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
</style>
{% endblock %}
