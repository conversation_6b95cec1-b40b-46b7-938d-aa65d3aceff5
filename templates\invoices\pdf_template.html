<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #333;
        }
        
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .invoice-details, .customer-details {
            width: 48%;
        }
        
        .invoice-details h3, .customer-details h3 {
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .info-row {
            margin-bottom: 8px;
        }
        
        .label {
            font-weight: bold;
            color: #555;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .items-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .totals-section {
            margin-top: 20px;
            text-align: left;
            width: 50%;
            margin-left: auto;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .total-row.final {
            border-top: 2px solid #007bff;
            border-bottom: 2px solid #007bff;
            font-weight: bold;
            font-size: 16px;
            color: #007bff;
        }
        
        .currency {
            font-weight: bold;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-right: 4px solid #007bff;
        }
        
        .notes h4 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-info">
            <div class="company-name">{{ company_name }}</div>
            <div>{{ company_address }}</div>
            <div>هاتف: {{ company_phone }} | البريد الإلكتروني: {{ company_email }}</div>
        </div>
    </div>

    <!-- Invoice Title -->
    <div class="invoice-title">
        فاتورة رقم: {{ invoice.invoice_number }}
        <span class="status-badge {% if invoice.is_paid %}status-paid{% elif invoice.is_overdue %}status-overdue{% else %}status-pending{% endif %}">
            {% if invoice.is_paid %}مدفوعة{% elif invoice.is_overdue %}متأخرة{% else %}معلقة{% endif %}
        </span>
    </div>

    <!-- Invoice and Customer Info -->
    <div class="invoice-info">
        <div class="invoice-details">
            <h3>تفاصيل الفاتورة</h3>
            <div class="info-row">
                <span class="label">رقم الفاتورة:</span> {{ invoice.invoice_number }}
            </div>
            <div class="info-row">
                <span class="label">تاريخ الإصدار:</span> {{ invoice.issue_date|date:"d/m/Y" }}
            </div>
            <div class="info-row">
                <span class="label">تاريخ الاستحقاق:</span> {{ invoice.due_date|date:"d/m/Y" }}
            </div>
            <div class="info-row">
                <span class="label">رقم الطلب:</span> {{ invoice.order.order_number }}
            </div>
        </div>

        <div class="customer-details">
            <h3>بيانات العميل</h3>
            <div class="info-row">
                <span class="label">اسم العميل:</span> {{ invoice.customer.name }}
            </div>
            {% if invoice.customer.company_name %}
            <div class="info-row">
                <span class="label">اسم الشركة:</span> {{ invoice.customer.company_name }}
            </div>
            {% endif %}
            <div class="info-row">
                <span class="label">رقم الهاتف:</span> {{ invoice.customer.phone }}
            </div>
            {% if invoice.customer.address %}
            <div class="info-row">
                <span class="label">العنوان:</span> {{ invoice.customer.address }}
            </div>
            {% endif %}
            {% if invoice.customer.city %}
            <div class="info-row">
                <span class="label">المدينة:</span> {{ invoice.customer.city }}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th>الوصف</th>
                <th>الكمية</th>
                <th>سعر الوحدة (د.أ)</th>
                <th>المجموع (د.أ)</th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice.items.all %}
            <tr>
                <td>{{ item.description }}</td>
                <td>{{ item.quantity|floatformat:2 }} كغم</td>
                <td class="currency">{{ item.unit_price|floatformat:3 }}</td>
                <td class="currency">{{ item.total_price|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Totals Section -->
    <div class="totals-section">
        <div class="total-row">
            <span>المجموع الفرعي:</span>
            <span class="currency">{{ invoice.subtotal|floatformat:2 }} د.أ</span>
        </div>
        
        {% if invoice.discount_amount > 0 %}
        <div class="total-row">
            <span>الخصم ({{ invoice.discount_rate }}%):</span>
            <span class="currency">-{{ invoice.discount_amount|floatformat:2 }} د.أ</span>
        </div>
        {% endif %}
        
        {% if invoice.tax_amount > 0 %}
        <div class="total-row">
            <span>الضريبة ({{ invoice.tax_rate }}%):</span>
            <span class="currency">{{ invoice.tax_amount|floatformat:2 }} د.أ</span>
        </div>
        {% endif %}
        
        <div class="total-row final">
            <span>المجموع الإجمالي:</span>
            <span class="currency">{{ invoice.total_amount|floatformat:2 }} د.أ</span>
        </div>
        
        <div class="total-row">
            <span>المبلغ المدفوع:</span>
            <span class="currency">{{ invoice.paid_amount|floatformat:2 }} د.أ</span>
        </div>
        
        <div class="total-row">
            <span>المبلغ المتبقي:</span>
            <span class="currency">{{ invoice.remaining_amount|floatformat:2 }} د.أ</span>
        </div>
    </div>

    <!-- Notes -->
    {% if invoice.notes %}
    <div class="notes">
        <h4>ملاحظات:</h4>
        <p>{{ invoice.notes }}</p>
    </div>
    {% endif %}

    <!-- Terms and Conditions -->
    {% if invoice.terms_conditions %}
    <div class="notes">
        <h4>الشروط والأحكام:</h4>
        <p>{{ invoice.terms_conditions }}</p>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>شكراً لتعاملكم معنا | {{ company_name }}</p>
        <p>تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"d/m/Y H:i" }}</p>
    </div>
</body>
</html>
