# Generated by Django 5.2.1 on 2025-06-03 18:52

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('mixtures', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم العميل')),
                ('company_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم الشركة')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('credit_limit', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='حد الائتمان (دينار)')),
                ('current_balance', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الرصيد الحالي (دينار)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('required_date', models.DateField(verbose_name='التاريخ المطلوب')),
                ('delivery_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('confirmed', 'مؤكد'), ('in_production', 'قيد الإنتاج'), ('ready', 'جاهز'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('payment_status', models.CharField(choices=[('unpaid', 'غير مدفوع'), ('partial', 'مدفوع جزئياً'), ('paid', 'مدفوع بالكامل')], default='unpaid', max_length=20, verbose_name='حالة الدفع')),
                ('subtotal', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='المجموع الفرعي (دينار)')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='نسبة الخصم %')),
                ('discount_amount', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='مبلغ الخصم (دينار)')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='نسبة الضريبة %')),
                ('tax_amount', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='مبلغ الضريبة (دينار)')),
                ('total_amount', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='المبلغ الإجمالي (دينار)')),
                ('paid_amount', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='المبلغ المدفوع (دينار)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('delivery_address', models.TextField(blank=True, null=True, verbose_name='عنوان التسليم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_orders', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='orders.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_kg', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية (كغم)')),
                ('unit_price', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الوحدة (دينار/كغم)')),
                ('total_price', models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=12, verbose_name='السعر الإجمالي (دينار)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('mixture', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mixtures.feedmixture', verbose_name='خلطة العلف')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'عنصر الطلب',
                'verbose_name_plural': 'عناصر الطلب',
                'unique_together': {('order', 'mixture')},
            },
        ),
    ]
