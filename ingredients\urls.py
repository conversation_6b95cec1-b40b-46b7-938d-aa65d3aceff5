from django.urls import path
from . import views

app_name = 'ingredients'

urlpatterns = [
    path('', views.ingredient_list, name='list'),
    path('add/', views.ingredient_add, name='add'),
    path('<int:pk>/', views.ingredient_detail, name='detail'),
    path('<int:pk>/edit/', views.ingredient_edit, name='edit'),
    path('<int:pk>/delete/', views.ingredient_delete, name='delete'),
    path('categories/', views.category_list, name='categories'),
    path('categories/add/', views.category_add, name='category_add'),
    path('categories/<int:pk>/edit/', views.category_edit, name='category_edit'),
    path('categories/<int:pk>/delete/', views.category_delete, name='category_delete'),
]
