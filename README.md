# 🏭 نظام إدارة مصنع الأعلاف

نظام ويب احترافي مطور بـ Django لإدارة مصنع إنتاج خلطات الأعلاف، يدعم اللغة العربية والعملة المحلية (الدينار الأردني).

## ✨ المميزات الرئيسية

### 🌾 إدارة المكونات
- إضافة وتعديل مكونات الأعلاف مع القيم الغذائية الكاملة
- تصنيف المكونات حسب الفئات (حبوب، بروتينات، فيتامينات، إلخ)
- إدارة المخزون مع تنبيهات المخزون المنخفض
- تتبع التكاليف والموردين
- نماذج إدخال بيانات احترافية مع التحقق من الصحة
- بحث وتصفية متقدم مع تصفح الصفحات
- صفحات تفاصيل شاملة مع عرض بصري للبيانات

### 🧪 تصميم خلطات الأعلاف
- إنشاء خلطات مخصصة بنسب محددة من المكونات
- حساب القيم الغذائية تلقائياً (البروتين، الطاقة، الألياف)
- حساب التكلفة وهامش الربح
- التحقق من اكتمال الصيغة (مجموع النسب = 100%)

### 📋 إدارة الطلبات والمبيعات
- إدارة بيانات العملاء مع حدود الائتمان
- إنشاء طلبات مع عناصر متعددة
- حساب الضرائب والخصومات تلقائياً
- تتبع حالة الطلبات والدفعات
- إنشاء الفواتير

### 📊 التحليلات والتقارير
- لوحة تحكم شاملة مع الإحصائيات
- تتبع دفعات الإنتاج والتكاليف
- تقارير المبيعات والأرباح
- إمكانية تصدير التقارير (PDF/Excel)

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن للمستخدمين
- مستويات صلاحيات متعددة
- لوحة إدارة Django المتقدمة

### 🌐 واجهة مستخدم متطورة
- تصميم متجاوب يدعم جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- واجهة حديثة باستخدام Bootstrap 5
- تجربة مستخدم سهلة وبديهية
- نماذج تفاعلية مع Crispy Forms
- بحث وتصفية متقدم في الوقت الفعلي
- تحذيرات وإشعارات ذكية

### 🔌 API REST شامل
- API كامل لجميع وظائف النظام
- مصادقة آمنة ونظام صلاحيات
- تصفية وبحث متقدم عبر API
- استجابات JSON منظمة ومفصلة
- دعم عمليات CRUD لجميع البيانات
- إحصائيات وتقارير عبر API
- وظائف متقدمة مثل تحديث المخزون وحساب التكاليف

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.2.1
- **Database**: SQLite (قابل للتطوير لـ PostgreSQL/MySQL)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5 RTL
- **API**: Django REST Framework
- **التقارير**: ReportLab (PDF), OpenPyXL (Excel)
- **الأيقونات**: Font Awesome 6
- **الخطوط**: Google Fonts (Cairo)

## 📦 التثبيت والإعداد

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd feed_factory
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install django djangorestframework pillow reportlab openpyxl django-bootstrap5 django-crispy-forms crispy-bootstrap5 psycopg2-binary
```

4. **تطبيق الهجرات**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **إضافة البيانات التجريبية (اختياري)**
```bash
python add_sample_data.py
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح النظام**
- النظام الرئيسي: http://127.0.0.1:8000/
- لوحة الإدارة: http://127.0.0.1:8000/admin/

## 👤 بيانات تسجيل الدخول التجريبية

إذا قمت بتشغيل ملف البيانات التجريبية:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123456

## 📁 هيكل المشروع

```
feed_factory/
├── feed_factory/          # إعدادات المشروع الرئيسية
├── core/                  # التطبيق الأساسي ولوحة التحكم
├── ingredients/           # إدارة المكونات
├── mixtures/             # إدارة خلطات الأعلاف
├── orders/               # إدارة الطلبات والعملاء
├── analytics/            # التحليلات والتقارير
├── templates/            # قوالب HTML
├── static/               # الملفات الثابتة
├── media/                # ملفات الوسائط
├── add_sample_data.py    # إضافة بيانات تجريبية
└── manage.py             # أداة إدارة Django
```

## 🚀 الاستخدام

### 1. لوحة التحكم الرئيسية
- عرض الإحصائيات السريعة
- الطلبات الحديثة
- تنبيهات المخزون المنخفض
- أفضل الخلطات مبيعاً

### 2. إدارة المكونات
- إضافة مكونات جديدة مع القيم الغذائية
- تحديث أسعار وكميات المخزون
- تصنيف المكونات حسب الفئات

### 3. تصميم الخلطات
- إنشاء خلطات جديدة
- إضافة المكونات بنسب محددة
- مراجعة القيم الغذائية والتكلفة

### 4. إدارة الطلبات
- إضافة عملاء جدد
- إنشاء طلبات مع عناصر متعددة
- تتبع حالة الطلبات والمدفوعات

## 🔧 التخصيص والتطوير

### إضافة مكونات جديدة
```python
# في ingredients/models.py
# يمكن إضافة حقول جديدة للمكونات
```

### تخصيص التقارير
```python
# في analytics/views.py
# يمكن إضافة تقارير مخصصة
```

### تغيير العملة
```python
# في feed_factory/settings.py
CURRENCY_CODE = 'USD'  # تغيير العملة
CURRENCY_SYMBOL = '$'  # تغيير رمز العملة
```

## 📈 خطط التطوير المستقبلية

- [ ] تطبيق موبايل (React Native/Flutter)
- [ ] نظام إدارة المخزون المتقدم
- [ ] تكامل مع أنظمة المحاسبة
- [ ] تقارير تحليلية متقدمة
- [ ] نظام إشعارات في الوقت الفعلي
- [ ] دعم متعدد اللغات
- [ ] نظام النسخ الاحتياطي التلقائي

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطوير هذا النظام بـ ❤️ لخدمة صناعة الأعلاف في العراق والمنطقة العربية**
