{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:detail' order.pk %}">{{ order.order_number }}</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <!-- معلومات الطلب -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> معلومات الطلب</h6>
                    <p class="mb-1"><strong>رقم الطلب:</strong> {{ order.order_number }}</p>
                    <p class="mb-1"><strong>العميل:</strong> {{ order.customer.name }}</p>
                    <p class="mb-0"><strong>المبلغ الحالي:</strong> <span class="currency">{{ order.total_amount|floatformat:3 }} د.ع</span></p>
                </div>

                <form method="post" id="itemForm">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.mixture.label_tag }}
                                {{ form.mixture }}
                                {% if form.mixture.errors %}
                                    <div class="text-danger">{{ form.mixture.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.quantity_kg.label_tag }}
                                {{ form.quantity_kg }}
                                {% if form.quantity_kg.errors %}
                                    <div class="text-danger">{{ form.quantity_kg.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.unit_price.label_tag }}
                                {{ form.unit_price }}
                                {% if form.unit_price.errors %}
                                    <div class="text-danger">{{ form.unit_price.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">السعر بالدينار لكل كيلوغرام</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المبلغ الإجمالي</label>
                                <input type="text" class="form-control" id="totalPrice" readonly placeholder="0.000">
                                <small class="form-text text-muted">يتم حسابه تلقائياً</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label_tag }}
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:detail' order.pk %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if item %}تحديث العنصر{% else %}إضافة العنصر{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if item %}
        <!-- معلومات إضافية للعنصر الموجود -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات العنصر
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>نوع الخلطة:</strong> {{ item.mixture.name }}</p>
                        <p><strong>نوع العلف:</strong> {{ item.mixture.feed_type.name }}</p>
                        <p><strong>تكلفة الخلطة:</strong> <span class="currency">{{ item.mixture.cost_per_kg|floatformat:3 }} د.ع/كغم</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الكمية الحالية:</strong> {{ item.quantity_kg|floatformat:2 }} كغم</p>
                        <p><strong>السعر الحالي:</strong> <span class="currency">{{ item.unit_price|floatformat:3 }} د.ع/كغم</span></p>
                        <p><strong>المبلغ الحالي:</strong> <span class="currency">{{ item.total_price|floatformat:3 }} د.ع</span></p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.querySelector('input[name="quantity_kg"]');
    const priceInput = document.querySelector('input[name="unit_price"]');
    const totalPriceInput = document.getElementById('totalPrice');
    const mixtureSelect = document.querySelector('select[name="mixture"]');
    
    // حساب المبلغ الإجمالي
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;
        totalPriceInput.value = total.toFixed(3) + ' د.أ';
    }
    
    // تحديث السعر عند اختيار خلطة (يمكن تطويره لاحقاً لجلب السعر من الخادم)
    function updatePriceFromMixture() {
        // يمكن إضافة AJAX هنا لجلب سعر الخلطة المقترح
    }
    
    // ربط الأحداث
    if (quantityInput) {
        quantityInput.addEventListener('input', calculateTotal);
    }
    
    if (priceInput) {
        priceInput.addEventListener('input', calculateTotal);
    }
    
    if (mixtureSelect) {
        mixtureSelect.addEventListener('change', updatePriceFromMixture);
    }
    
    // حساب أولي
    calculateTotal();
});
</script>
{% endblock %}
