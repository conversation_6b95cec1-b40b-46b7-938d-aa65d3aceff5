{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'mixtures:list' %}">خلطات الأعلاف</a></li>
        <li class="breadcrumb-item"><a href="{% url 'mixtures:detail' mixture.pk %}">{{ mixture.name }}</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                {% load crispy_forms_tags %}
                {% crispy form %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات الخلطة -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الخلطة
                </h6>
            </div>
            <div class="card-body">
                <p><strong>اسم الخلطة:</strong> {{ mixture.name }}</p>
                <p><strong>نوع العلف:</strong> {{ mixture.feed_type.name }}</p>
                <p><strong>النسب الحالية:</strong> {{ mixture.total_components_percentage|floatformat:1 }}%</p>
                <p><strong>النسبة المتبقية:</strong>
                    <span class="{% if mixture.total_components_percentage < 100 %}text-success{% else %}text-danger{% endif %} remaining-percentage">
                        {{ remaining_percentage|floatformat:1 }}%
                    </span>
                </p>
            </div>
        </div>
        
        <!-- المكونات الحالية -->
        {% if mixture.components.exists %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i>
                    المكونات الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المكون</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for component in mixture.components.all %}
                            <tr>
                                <td>{{ component.ingredient.name|truncatechars:20 }}</td>
                                <td>{{ component.percentage|floatformat:1 }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th>الإجمالي</th>
                                <th>{{ mixture.total_components_percentage|floatformat:1 }}%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'mixtures:detail' mixture.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للخلطة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث النسبة المتبقية عند تغيير النسبة
document.addEventListener('DOMContentLoaded', function() {
    const percentageInput = document.querySelector('input[name="percentage"]');
    const currentTotal = {{ mixture.total_components_percentage|floatformat:1 }};
    
    if (percentageInput) {
        percentageInput.addEventListener('input', function() {
            const newPercentage = parseFloat(this.value) || 0;
            const newTotal = currentTotal + newPercentage;
            const remaining = 100 - newTotal;
            
            // تحديث عرض النسبة المتبقية
            const remainingElement = document.querySelector('.remaining-percentage');
            if (remainingElement) {
                remainingElement.textContent = remaining.toFixed(1) + '%';
                remainingElement.className = remaining >= 0 ? 'text-success' : 'text-danger';
            }
            
            // تحذير إذا تجاوزت النسبة 100%
            if (newTotal > 100) {
                this.setCustomValidity('مجموع النسب سيتجاوز 100%');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>
{% endblock %}
