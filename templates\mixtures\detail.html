{% extends 'base.html' %}

{% block title %}{{ mixture.name }} - خلطات الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'mixtures:list' %}">خلطات الأعلاف</a></li>
        <li class="breadcrumb-item active">{{ mixture.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3">
                    <i class="fas fa-blender"></i>
                    {{ mixture.name }}
                </h1>
                <p class="text-muted mb-0">{{ mixture.feed_type.name }} - {{ mixture.feed_type.target_animal }}</p>
            </div>
            <div>
                <div class="btn-group" role="group">
                    <a href="{% url 'mixtures:edit' mixture.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <a href="{% url 'mixtures:component_add' mixture.pk %}" class="btn btn-success">
                        <i class="fas fa-plus"></i>
                        إضافة مكون
                    </a>
                    <button type="button" class="btn btn-info" onclick="calculateMixture()">
                        <i class="fas fa-calculator"></i>
                        إعادة حساب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات أساسية -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                {% if mixture.description %}
                <p><strong>الوصف:</strong> {{ mixture.description }}</p>
                {% endif %}

                <div class="row">
                    <div class="col-md-6">
                        <p><strong>نوع العلف:</strong> {{ mixture.feed_type.name }}</p>
                        <p><strong>الحيوان المستهدف:</strong> {{ mixture.feed_type.target_animal }}</p>
                        <p><strong>حجم الدفعة:</strong> {{ mixture.batch_size_kg|floatformat:2 }} كغم</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ mixture.created_at|date:"Y/m/d H:i" }}</p>
                        <p><strong>آخر تحديث:</strong> {{ mixture.updated_at|date:"Y/m/d H:i" }}</p>
                        <p><strong>الحالة:</strong>
                            {% if mixture.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if mixture.notes %}
                <hr>
                <p><strong>ملاحظات:</strong></p>
                <p class="text-muted">{{ mixture.notes }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i>
                    حالة الصيغة
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    {% if mixture.is_formula_complete %}
                        <i class="fas fa-check-circle fa-3x text-success"></i>
                        <h5 class="text-success mt-2">صيغة مكتملة</h5>
                    {% else %}
                        <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                        <h5 class="text-warning mt-2">صيغة غير مكتملة</h5>
                    {% endif %}
                </div>
                <p class="mb-0">
                    <strong>إجمالي النسب:</strong><br>
                    <span class="h4 {% if mixture.is_formula_complete %}text-success{% else %}text-warning{% endif %}">
                        {{ mixture.total_components_percentage|floatformat:1 }}%
                    </span>
                </p>
                <small class="text-muted">
                    {% if not mixture.is_formula_complete %}
                        المطلوب: {{ 100|floatformat:0 }}%
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- القيم الغذائية والتكلفة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    القيم الغذائية
                </h5>
            </div>
            <div class="card-body">
                {% with nutritional_values=mixture.calculate_nutritional_values %}
                <div class="row">
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h6>البروتين</h6>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar {% if nutritional_values.protein_percentage >= mixture.target_protein_percentage %}bg-success{% else %}bg-warning{% endif %}"
                                     style="width: {{ nutritional_values.protein_percentage }}%">
                                    {{ nutritional_values.protein_percentage|floatformat:1 }}%
                                </div>
                            </div>
                            <small class="text-muted">المستهدف: {{ mixture.target_protein_percentage|floatformat:1 }}%</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h6>الألياف</h6>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar {% if nutritional_values.fiber_percentage <= mixture.target_fiber_percentage %}bg-success{% else %}bg-warning{% endif %}"
                                     style="width: {{ nutritional_values.fiber_percentage }}%">
                                    {{ nutritional_values.fiber_percentage|floatformat:1 }}%
                                </div>
                            </div>
                            <small class="text-muted">المستهدف: {{ mixture.target_fiber_percentage|floatformat:1 }}%</small>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <h6>الطاقة</h6>
                    <h4 class="{% if nutritional_values.energy_kcal_per_kg >= mixture.target_energy_kcal_per_kg %}text-success{% else %}text-warning{% endif %}">
                        {{ nutritional_values.energy_kcal_per_kg|floatformat:0 }}
                    </h4>
                    <small class="text-muted">ك.كالوري/كغم (المستهدف: {{ mixture.target_energy_kcal_per_kg|floatformat:0 }})</small>
                </div>
                {% endwith %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-dollar-sign"></i>
                    التكلفة والربحية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h6>تكلفة الإنتاج</h6>
                        <h4 class="text-info currency">{{ mixture.cost_per_kg|floatformat:3 }}</h4>
                        <small class="text-muted">د.ع/كغم</small>
                    </div>
                    <div class="col-4">
                        <h6>سعر البيع</h6>
                        <h4 class="text-primary currency">{{ mixture.selling_price_per_kg|floatformat:3 }}</h4>
                        <small class="text-muted">د.ع/كغم</small>
                    </div>
                    <div class="col-4">
                        <h6>هامش الربح</h6>
                        <h4 class="{% if mixture.profit_margin_percentage > 20 %}text-success{% elif mixture.profit_margin_percentage > 10 %}text-warning{% else %}text-danger{% endif %}">
                            {{ mixture.profit_margin_percentage|floatformat:1 }}%
                        </h4>
                        <small class="text-muted">من سعر البيع</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <p class="mb-0"><strong>الربح لكل كيلوغرام:</strong></p>
                    <h5 class="currency {% if mixture.profit_margin_percentage > 0 %}text-success{% else %}text-danger{% endif %}">
                        {{ mixture.profit_per_kg|floatformat:3 }} د.ع
                    </h5>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مكونات الخلطة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>
                        مكونات الخلطة ({{ components.count }})
                    </h5>
                    <a href="{% url 'mixtures:component_add' mixture.pk %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i>
                        إضافة مكون
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if components %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المكون</th>
                                    <th>الفئة</th>
                                    <th>النسبة %</th>
                                    <th>الوزن في الدفعة</th>
                                    <th>البروتين</th>
                                    <th>الطاقة</th>
                                    <th>التكلفة</th>
                                    <th>مساهمة التكلفة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for component in components %}
                                <tr>
                                    <td>
                                        <strong>{{ component.ingredient.name }}</strong>
                                        {% if component.notes %}
                                            <br><small class="text-muted">{{ component.notes|truncatechars:30 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ component.ingredient.category.name }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ component.percentage|floatformat:1 }}%</span>
                                    </td>
                                    <td>{{ component.weight_in_batch|floatformat:2 }} كغم</td>
                                    <td>{{ component.ingredient.protein_percentage|floatformat:1 }}%</td>
                                    <td>{{ component.ingredient.energy_kcal_per_kg|floatformat:0 }}</td>
                                    <td class="currency">{{ component.ingredient.cost_per_kg|floatformat:3 }} د.ع</td>
                                    <td class="currency">{{ component.cost_contribution|floatformat:3 }} د.ع</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'mixtures:component_edit' mixture.pk component.pk %}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'mixtures:component_delete' mixture.pk component.pk %}"
                                               class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <th colspan="2">الإجمالي</th>
                                    <th>{{ mixture.total_components_percentage|floatformat:1 }}%</th>
                                    <th>{{ mixture.batch_size_kg|floatformat:2 }} كغم</th>
                                    <th colspan="3"></th>
                                    <th class="currency">{{ mixture.cost_per_kg|floatformat:3 }} د.ع</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مكونات في هذه الخلطة</h5>
                        <p class="text-muted">ابدأ بإضافة المكونات لإنشاء صيغة الخلطة</p>
                        <a href="{% url 'mixtures:component_add' mixture.pk %}" class="btn btn-success">
                            <i class="fas fa-plus"></i>
                            إضافة أول مكون
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إجراءات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="btn-group" role="group">
            <a href="{% url 'mixtures:list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
            <a href="{% url 'mixtures:edit' mixture.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i>
                تعديل الخلطة
            </a>
            {% if not mixture.is_formula_complete %}
                <button type="button" class="btn btn-info" onclick="showOptimizationTips()">
                    <i class="fas fa-lightbulb"></i>
                    نصائح التحسين
                </button>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function calculateMixture() {
    fetch('{% url "mixtures:calculate" mixture.pk %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إعادة حساب القيم بنجاح!');
            location.reload();
        } else {
            alert('حدث خطأ أثناء الحساب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الحساب');
    });
}

function showOptimizationTips() {
    const currentTotal = {{ mixture.total_components_percentage|floatformat:1 }};
    const remaining = 100 - currentTotal;

    let message = `نصائح لتحسين الخلطة:\n\n`;
    message += `• النسبة المتبقية: ${remaining.toFixed(1)}%\n`;

    if (remaining > 0) {
        message += `• أضف مكونات أخرى لتكملة الصيغة\n`;
        message += `• تأكد من توازن القيم الغذائية\n`;
        message += `• راجع التكلفة مقابل الجودة\n`;
    } else if (remaining < 0) {
        message += `• قلل نسب بعض المكونات\n`;
        message += `• إجمالي النسب يتجاوز 100%\n`;
    }

    alert(message);
}

// تأكيد الحذف
document.querySelectorAll('a[href*="delete"]').forEach(function(link) {
    link.addEventListener('click', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا المكون؟')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
