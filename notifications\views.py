from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from .models import Notification
from .services import NotificationService


@login_required
def notification_list(request):
    """قائمة جميع الإشعارات"""
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')

    context = {
        'notifications': notifications,
        'unread_count': NotificationService.get_unread_count(request.user),
    }

    return render(request, 'notifications/list.html', context)


@login_required
@require_POST
def mark_as_read(request, notification_id):
    """تحديد إشعار كمقروء"""
    notification = get_object_or_404(Notification, pk=notification_id, user=request.user)
    notification.mark_as_read()

    return JsonResponse({'success': True})


@login_required
@require_POST
def mark_all_as_read(request):
    """تحديد جميع الإشعارات كمقروءة"""
    count = NotificationService.mark_all_as_read(request.user)

    return JsonResponse({'success': True, 'marked_count': count})


@login_required
def notification_count(request):
    """عدد الإشعارات غير المقروءة"""
    count = NotificationService.get_unread_count(request.user)

    return JsonResponse({'count': count})
