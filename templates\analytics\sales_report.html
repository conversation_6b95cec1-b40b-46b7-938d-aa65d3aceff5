{% extends 'base.html' %}

{% block title %}تقرير المبيعات - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'analytics:dashboard' %}">التحليلات والتقارير</a></li>
        <li class="breadcrumb-item active">تقرير المبيعات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-chart-line text-primary"></i>
                تقرير المبيعات
            </h1>
            <div>
                <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter"></i>
                    تصفية التقرير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?period=today">اليوم</a></li>
                    <li><a class="dropdown-item" href="?period=week">هذا الأسبوع</a></li>
                    <li><a class="dropdown-item" href="?period=month">هذا الشهر</a></li>
                    <li><a class="dropdown-item" href="?period=year">هذا العام</a></li>
                </ul>
                <a href="{% url 'analytics:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للتحليلات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات المبيعات -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المبيعات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_sales|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            عدد الطلبات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_orders }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            متوسط قيمة الطلب
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ avg_order_value|floatformat:2 }} د.أ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calculator fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            عدد العملاء
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_customers }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">تطور المبيعات</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جداول التفاصيل -->
<div class="row">
    <!-- أفضل المنتجات مبيعاً -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">أفضل المنتجات مبيعاً</h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.total_sold|floatformat:0 }} كغم</td>
                                    <td class="currency">{{ product.total_revenue|floatformat:2 }} د.أ</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <p>لا توجد بيانات مبيعات</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- أفضل العملاء -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">أفضل العملاء</h6>
            </div>
            <div class="card-body">
                {% if top_customers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي المشتريات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in top_customers %}
                                <tr>
                                    <td>{{ customer.name }}</td>
                                    <td>{{ customer.total_orders }}</td>
                                    <td class="currency">{{ customer.total_spent|floatformat:2 }} د.أ</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا توجد بيانات عملاء</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني لتطور المبيعات
const ctx = document.getElementById('salesTrendChart').getContext('2d');
const salesTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ sales_labels|safe }},
        datasets: [{
            label: 'المبيعات (د.أ)',
            data: {{ sales_data|safe }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'تطور المبيعات خلال الفترة المحددة'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' د.أ';
                    }
                }
            }
        }
    }
});
</script>
{% endblock %}
