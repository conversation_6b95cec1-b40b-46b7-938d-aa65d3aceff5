from django import forms
from django.core.exceptions import ValidationError
from .models import Order, Customer, OrderItem
from mixtures.models import FeedMixture


class OrderForm(forms.ModelForm):
    """نموذج إضافة/تعديل الطلب"""
    
    class Meta:
        model = Order
        fields = [
            'customer', 'required_date', 'discount_percentage',
            'delivery_address', 'notes'
        ]
        widgets = {
            'customer': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'required_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': True
            }),
            'discount_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'delivery_address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'عنوان التسليم (اختياري)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية (اختياري)'
            }),
        }
        labels = {
            'customer': 'العميل',
            'required_date': 'التاريخ المطلوب',
            'discount_percentage': 'نسبة الخصم (%)',
            'delivery_address': 'عنوان التسليم',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تحديد العملاء النشطين فقط
        self.fields['customer'].queryset = Customer.objects.filter(is_active=True)
        
        # إضافة خيار فارغ للعميل
        self.fields['customer'].empty_label = "اختر العميل"


class CustomerForm(forms.ModelForm):
    """نموذج إضافة/تعديل العميل"""
    
    class Meta:
        model = Customer
        fields = [
            'name', 'company_name', 'phone', 'email',
            'address', 'city', 'credit_limit', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'required': True,
                'placeholder': 'اسم العميل'
            }),
            'company_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الشركة (اختياري)'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'required': True,
                'placeholder': 'رقم الهاتف'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني (اختياري)'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان (اختياري)'
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المدينة (اختياري)'
            }),
            'credit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0',
                'placeholder': '0.000'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية (اختياري)'
            }),
        }
        labels = {
            'name': 'اسم العميل',
            'company_name': 'اسم الشركة',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'city': 'المدينة',
            'credit_limit': 'حد الائتمان (دينار)',
            'notes': 'ملاحظات'
        }

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone and len(phone) < 8:
            raise ValidationError('رقم الهاتف يجب أن يكون 8 أرقام على الأقل')
        return phone


class OrderItemForm(forms.ModelForm):
    """نموذج إضافة/تعديل عنصر الطلب"""
    
    class Meta:
        model = OrderItem
        fields = ['mixture', 'quantity_kg', 'unit_price', 'notes']
        widgets = {
            'mixture': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'quantity_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01',
                'required': True,
                'placeholder': '0.00'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001',
                'required': True,
                'placeholder': '0.000'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات إضافية (اختياري)'
            }),
        }
        labels = {
            'mixture': 'نوع الخلطة',
            'quantity_kg': 'الكمية (كغم)',
            'unit_price': 'سعر الوحدة (دينار/كغم)',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        order = kwargs.pop('order', None)
        super().__init__(*args, **kwargs)
        
        # تحديد الخلطات النشطة فقط
        self.fields['mixture'].queryset = FeedMixture.objects.filter(is_active=True)
        
        # إضافة خيار فارغ للخلطة
        self.fields['mixture'].empty_label = "اختر نوع الخلطة"
        
        # إذا كان هناك طلب، استبعد الخلطات الموجودة بالفعل (للإضافة فقط)
        if order and not self.instance.pk:
            existing_mixtures = order.items.values_list('mixture_id', flat=True)
            self.fields['mixture'].queryset = self.fields['mixture'].queryset.exclude(
                id__in=existing_mixtures
            )

    def clean_quantity_kg(self):
        quantity = self.cleaned_data.get('quantity_kg')
        if quantity and quantity <= 0:
            raise ValidationError('الكمية يجب أن تكون أكبر من صفر')
        return quantity

    def clean_unit_price(self):
        price = self.cleaned_data.get('unit_price')
        if price and price <= 0:
            raise ValidationError('السعر يجب أن يكون أكبر من صفر')
        return price


class OrderSearchForm(forms.Form):
    """نموذج البحث في الطلبات"""

    STATUS_CHOICES = [
        ('', 'جميع الحالات'),
        ('pending', 'في الانتظار'),
        ('confirmed', 'مؤكد'),
        ('in_production', 'قيد الإنتاج'),
        ('ready', 'جاهز'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('', 'جميع حالات الدفع'),
        ('unpaid', 'غير مدفوع'),
        ('partial', 'مدفوع جزئياً'),
        ('paid', 'مدفوع بالكامل'),
    ]

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث برقم الطلب أو اسم العميل...'
        }),
        label='البحث'
    )

    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='حالة الطلب'
    )

    payment_status = forms.ChoiceField(
        choices=PAYMENT_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='حالة الدفع'
    )

    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        required=False,
        empty_label="جميع العملاء",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='العميل'
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='من تاريخ'
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='إلى تاريخ'
    )


class CustomerSearchForm(forms.Form):
    """نموذج البحث في العملاء"""

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث باسم العميل أو الشركة أو الهاتف...'
        }),
        label='البحث'
    )

    city = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'المدينة'
        }),
        label='المدينة'
    )

    is_active = forms.ChoiceField(
        choices=[
            ('', 'جميع العملاء'),
            ('true', 'نشط'),
            ('false', 'غير نشط'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='الحالة'
    )
