from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, F
from .models import Ingredient, IngredientCategory
from .forms import IngredientForm, IngredientCategoryForm, IngredientSearchForm


@login_required
def ingredient_list(request):
    """قائمة المكونات مع البحث والتصفية"""
    form = IngredientSearchForm(request.GET)
    ingredients = Ingredient.objects.select_related('category')

    # تطبيق الفلاتر
    if form.is_valid():
        search = form.cleaned_data.get('search')
        category = form.cleaned_data.get('category')
        stock_status = form.cleaned_data.get('stock_status')
        is_active = form.cleaned_data.get('is_active')

        if search:
            ingredients = ingredients.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(supplier__icontains=search)
            )

        if category:
            ingredients = ingredients.filter(category=category)

        if stock_status:
            if stock_status == 'available':
                ingredients = ingredients.filter(current_stock__gt=F('minimum_stock'))
            elif stock_status == 'low':
                ingredients = ingredients.filter(
                    current_stock__lte=F('minimum_stock'),
                    current_stock__gt=0
                )
            elif stock_status == 'out':
                ingredients = ingredients.filter(current_stock=0)

        if is_active:
            ingredients = ingredients.filter(is_active=(is_active == 'true'))

    # ترتيب النتائج
    ingredients = ingredients.order_by('name')

    # التصفح
    paginator = Paginator(ingredients, 20)  # 20 عنصر في كل صفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'ingredients': page_obj,
        'page_obj': page_obj,
    }

    return render(request, 'ingredients/list.html', context)


@login_required
def ingredient_detail(request, pk):
    """تفاصيل المكون"""
    ingredient = get_object_or_404(Ingredient, pk=pk)
    return render(request, 'ingredients/detail.html', {'ingredient': ingredient})


@login_required
def ingredient_add(request):
    """إضافة مكون جديد"""
    if request.method == 'POST':
        form = IngredientForm(request.POST)
        if form.is_valid():
            ingredient = form.save()
            messages.success(request, f'تم إضافة المكون "{ingredient.name}" بنجاح')
            return redirect('ingredients:detail', pk=ingredient.pk)
    else:
        form = IngredientForm()

    return render(request, 'ingredients/form.html', {
        'form': form,
        'title': 'إضافة مكون جديد'
    })


@login_required
def ingredient_edit(request, pk):
    """تعديل المكون"""
    ingredient = get_object_or_404(Ingredient, pk=pk)

    if request.method == 'POST':
        form = IngredientForm(request.POST, instance=ingredient)
        if form.is_valid():
            ingredient = form.save()
            messages.success(request, f'تم تحديث المكون "{ingredient.name}" بنجاح')
            return redirect('ingredients:detail', pk=pk)
    else:
        form = IngredientForm(instance=ingredient)

    return render(request, 'ingredients/form.html', {
        'form': form,
        'ingredient': ingredient,
        'title': f'تعديل المكون: {ingredient.name}'
    })


@login_required
def ingredient_delete(request, pk):
    """حذف المكون"""
    ingredient = get_object_or_404(Ingredient, pk=pk)
    if request.method == 'POST':
        ingredient.is_active = False
        ingredient.save()
        messages.success(request, 'تم حذف المكون بنجاح')
        return redirect('ingredients:list')

    return render(request, 'ingredients/delete.html', {'ingredient': ingredient})


@login_required
def category_list(request):
    """قائمة فئات المكونات"""
    from django.db.models import Count

    categories = IngredientCategory.objects.prefetch_related('ingredients').annotate(
        ingredients_count=Count('ingredients')
    ).all()

    # حساب الإحصائيات
    total_ingredients = Ingredient.objects.filter(is_active=True).count()
    avg_ingredients_per_category = total_ingredients / categories.count() if categories.count() > 0 else 0
    largest_category = categories.order_by('-ingredients_count').first() if categories.exists() else None

    context = {
        'categories': categories,
        'total_ingredients': total_ingredients,
        'avg_ingredients_per_category': avg_ingredients_per_category,
        'largest_category': largest_category,
    }

    return render(request, 'ingredients/categories.html', context)


@login_required
def category_add(request):
    """إضافة فئة جديدة"""
    if request.method == 'POST':
        form = IngredientCategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'تم إضافة الفئة "{category.name}" بنجاح')
            return redirect('ingredients:categories')
    else:
        form = IngredientCategoryForm()

    return render(request, 'ingredients/category_form.html', {
        'form': form,
        'title': 'إضافة فئة جديدة'
    })


@login_required
def category_edit(request, pk):
    """تعديل فئة المكونات"""
    category = get_object_or_404(IngredientCategory, pk=pk)

    if request.method == 'POST':
        form = IngredientCategoryForm(request.POST, instance=category)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'تم تحديث فئة "{category.name}" بنجاح')
            return redirect('ingredients:categories')
    else:
        form = IngredientCategoryForm(instance=category)

    return render(request, 'ingredients/category_form.html', {
        'form': form,
        'category': category
    })


@login_required
def category_delete(request, pk):
    """حذف فئة المكونات"""
    category = get_object_or_404(IngredientCategory, pk=pk)

    if request.method == 'POST':
        category_name = category.name
        ingredients_count = category.ingredients.count()
        category.delete()
        messages.success(request, f'تم حذف فئة "{category_name}" و {ingredients_count} مكون مرتبط بها')
        return redirect('ingredients:categories')

    return render(request, 'ingredients/category_delete.html', {'category': category})
