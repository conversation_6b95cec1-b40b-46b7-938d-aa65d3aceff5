{% extends 'base.html' %}

{% block title %}لوحة التحكم - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active">لوحة التحكم</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_ingredients }}</div>
            <div class="stats-label">
                <i class="fas fa-seedling"></i>
                المكونات النشطة
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_mixtures }}</div>
            <div class="stats-label">
                <i class="fas fa-blender"></i>
                خلطات الأعلاف
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_customers }}</div>
            <div class="stats-label">
                <i class="fas fa-users"></i>
                العملاء النشطون
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.pending_orders }}</div>
            <div class="stats-label">
                <i class="fas fa-clock"></i>
                طلبات في الانتظار
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.low_stock_ingredients }}</div>
            <div class="stats-label">
                <i class="fas fa-exclamation-triangle"></i>
                مخزون منخفض
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الطلبات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart"></i>
                    الطلبات الحديثة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.order_number }}</td>
                                    <td>{{ order.customer.name }}</td>
                                    <td>{{ order.order_date|date:"Y/m/d" }}</td>
                                    <td>
                                        <span class="status-badge 
                                            {% if order.status == 'pending' %}status-pending
                                            {% elif order.status == 'delivered' %}status-completed
                                            {% else %}status-active{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td class="currency">{{ order.total_amount|floatformat:3 }} د.ع</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'orders:list' %}" class="btn btn-primary btn-sm">
                            عرض جميع الطلبات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>لا توجد طلبات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- المكونات منخفضة المخزون -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    تنبيهات المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_ingredients %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المكون</th>
                                    <th>المخزون الحالي</th>
                                    <th>الحد الأدنى</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in low_stock_ingredients %}
                                <tr>
                                    <td>{{ ingredient.name }}</td>
                                    <td>{{ ingredient.current_stock|floatformat:2 }} كغم</td>
                                    <td>{{ ingredient.minimum_stock|floatformat:2 }} كغم</td>
                                    <td>
                                        <span class="status-badge 
                                            {% if ingredient.current_stock <= 0 %}status-inactive
                                            {% else %}status-pending{% endif %}">
                                            {{ ingredient.stock_status }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'ingredients:list' %}" class="btn btn-warning btn-sm">
                            إدارة المخزون
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                        <p>جميع المكونات متوفرة بكميات كافية</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أفضل الخلطات مبيعاً -->
{% if top_mixtures %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy text-warning"></i>
                    أفضل الخلطات مبيعاً
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الخلطة</th>
                                <th>نوع العلف</th>
                                <th>إجمالي المبيعات</th>
                                <th>سعر البيع</th>
                                <th>هامش الربح</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mixture in top_mixtures %}
                            <tr>
                                <td>{{ mixture.name }}</td>
                                <td>{{ mixture.feed_type.name }}</td>
                                <td>{{ mixture.total_sold|floatformat:2 }} كغم</td>
                                <td class="currency">{{ mixture.selling_price_per_kg|floatformat:3 }} د.أ</td>
                                <td>
                                    <span class="{% if mixture.profit_margin_percentage > 20 %}text-success{% elif mixture.profit_margin_percentage > 10 %}text-warning{% else %}text-danger{% endif %}">
                                        {{ mixture.profit_margin_percentage|floatformat:1 }}%
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الصفحة كل 5 دقائق
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
