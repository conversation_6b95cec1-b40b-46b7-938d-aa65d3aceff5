from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # الطلبات
    path('', views.order_list, name='list'),
    path('add/', views.order_add, name='add'),
    path('<int:pk>/', views.order_detail, name='detail'),
    path('<int:pk>/edit/', views.order_edit, name='edit'),
    path('<int:pk>/delete/', views.order_delete, name='delete'),
    path('<int:pk>/invoice/', views.order_invoice, name='invoice'),

    # عناصر الطلب
    path('<int:order_pk>/items/add/', views.order_item_add, name='item_add'),
    path('<int:order_pk>/items/<int:item_pk>/edit/', views.order_item_edit, name='item_edit'),
    path('<int:order_pk>/items/<int:item_pk>/delete/', views.order_item_delete, name='item_delete'),

    # العملاء
    path('customers/', views.customer_list, name='customers'),
    path('customers/add/', views.customer_add, name='customer_add'),
    path('customers/<int:pk>/', views.customer_detail, name='customer_detail'),
    path('customers/<int:pk>/edit/', views.customer_edit, name='customer_edit'),
    path('customers/<int:pk>/delete/', views.customer_delete, name='customer_delete'),
]
