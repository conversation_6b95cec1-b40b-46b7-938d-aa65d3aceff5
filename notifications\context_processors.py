from .models import Notification
from .services import NotificationService


def notifications_context(request):
    """Context processor للإشعارات"""
    if request.user.is_authenticated:
        # الإشعارات غير المقروءة
        unread_notifications = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).order_by('-created_at')[:5]  # آخر 5 إشعارات
        
        # عدد الإشعارات غير المقروءة
        unread_count = NotificationService.get_unread_count(request.user)
        
        return {
            'unread_notifications': unread_notifications,
            'unread_notifications_count': unread_count,
        }
    
    return {
        'unread_notifications': [],
        'unread_notifications_count': 0,
    }
