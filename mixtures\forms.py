from django import forms
from django.forms import inlineformset_factory
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div, Field
from crispy_forms.bootstrap import TabHolder, Tab
from .models import FeedMixture, FeedType, MixtureComponent
from ingredients.models import Ingredient


class FeedTypeForm(forms.ModelForm):
    """نموذج إضافة/تعديل نوع العلف"""
    
    class Meta:
        model = FeedType
        fields = ['name', 'target_animal', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-3'),
                Column('target_animal', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('description', css_class='form-group col-md-12 mb-3'),
                css_class='form-row'
            ),
            Submit('submit', 'حفظ', css_class='btn btn-primary')
        )


class FeedMixtureForm(forms.ModelForm):
    """نموذج إضافة/تعديل خلطة العلف"""
    
    class Meta:
        model = FeedMixture
        fields = [
            'name', 'feed_type', 'description',
            'target_protein_percentage', 'target_energy_kcal_per_kg', 'target_fiber_percentage',
            'selling_price_per_kg', 'batch_size_kg', 'notes'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            TabHolder(
                Tab('المعلومات الأساسية',
                    Row(
                        Column('name', css_class='form-group col-md-8 mb-3'),
                        Column('feed_type', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('description', css_class='form-group col-md-12 mb-3'),
                        css_class='form-row'
                    ),
                ),
                Tab('المتطلبات الغذائية',
                    Row(
                        Column('target_protein_percentage', css_class='form-group col-md-4 mb-3'),
                        Column('target_energy_kcal_per_kg', css_class='form-group col-md-4 mb-3'),
                        Column('target_fiber_percentage', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    HTML('<div class="alert alert-info"><i class="fas fa-info-circle"></i> هذه القيم المستهدفة للخلطة النهائية</div>'),
                ),
                Tab('التكلفة والإنتاج',
                    Row(
                        Column('selling_price_per_kg', css_class='form-group col-md-6 mb-3'),
                        Column('batch_size_kg', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('notes', css_class='form-group col-md-12 mb-3'),
                        css_class='form-row'
                    ),
                ),
            ),
            Submit('submit', 'حفظ الخلطة', css_class='btn btn-primary')
        )


class MixtureComponentForm(forms.ModelForm):
    """نموذج إضافة/تعديل مكون الخلطة"""
    
    class Meta:
        model = MixtureComponent
        fields = ['ingredient', 'percentage', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        mixture = kwargs.pop('mixture', None)
        super().__init__(*args, **kwargs)
        
        # تصفية المكونات المتاحة (استبعاد المكونات المضافة بالفعل)
        if mixture:
            existing_ingredients = mixture.components.values_list('ingredient_id', flat=True)
            self.fields['ingredient'].queryset = Ingredient.objects.filter(
                is_active=True
            ).exclude(id__in=existing_ingredients)
        else:
            self.fields['ingredient'].queryset = Ingredient.objects.filter(is_active=True)

        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('ingredient', css_class='form-group col-md-6 mb-3'),
                Column('percentage', css_class='form-group col-md-3 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('notes', css_class='form-group col-md-12 mb-3'),
                css_class='form-row'
            ),
            Submit('submit', 'إضافة المكون', css_class='btn btn-primary')
        )

    def clean_percentage(self):
        percentage = self.cleaned_data.get('percentage')
        if percentage <= 0:
            raise forms.ValidationError('النسبة يجب أن تكون أكبر من صفر')
        if percentage > 100:
            raise forms.ValidationError('النسبة لا يمكن أن تتجاوز 100%')
        return percentage


class MixtureSearchForm(forms.Form):
    """نموذج البحث في الخلطات"""
    
    search = forms.CharField(
        label='البحث',
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'ابحث في أسماء الخلطات...',
            'class': 'form-control'
        })
    )
    
    feed_type = forms.ModelChoiceField(
        label='نوع العلف',
        queryset=FeedType.objects.all(),
        required=False,
        empty_label='جميع الأنواع',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    min_protein = forms.DecimalField(
        label='الحد الأدنى للبروتين %',
        max_digits=5,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'min': '0',
            'max': '100'
        })
    )
    
    max_cost = forms.DecimalField(
        label='الحد الأقصى للتكلفة (د.أ/كغم)',
        max_digits=10,
        decimal_places=3,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.001',
            'min': '0'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'GET'
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='form-group col-md-4 mb-3'),
                Column('feed_type', css_class='form-group col-md-3 mb-3'),
                Column('min_protein', css_class='form-group col-md-2 mb-3'),
                Column('max_cost', css_class='form-group col-md-2 mb-3'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='form-group col-md-1 mb-3 d-flex align-items-end'
                ),
                css_class='form-row'
            )
        )


# إنشاء Formset للمكونات
MixtureComponentFormSet = inlineformset_factory(
    FeedMixture,
    MixtureComponent,
    form=MixtureComponentForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)
