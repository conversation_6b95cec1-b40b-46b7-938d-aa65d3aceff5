#!/usr/bin/env python
"""
إنشاء مستخدم إداري
"""
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
django.setup()

from django.contrib.auth.models import User

def create_superuser():
    """إنشاء مستخدم إداري"""
    
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123'
    
    if User.objects.filter(username=username).exists():
        print(f"ℹ️  المستخدم {username} موجود بالفعل")
        user = User.objects.get(username=username)
    else:
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        print(f"✅ تم إنشاء المستخدم الإداري: {username}")
    
    print(f"📧 البريد الإلكتروني: {user.email}")
    print(f"🔑 كلمة المرور: {password}")
    print(f"🔗 رابط تسجيل الدخول: http://127.0.0.1:8000/admin/")

if __name__ == '__main__':
    create_superuser()
