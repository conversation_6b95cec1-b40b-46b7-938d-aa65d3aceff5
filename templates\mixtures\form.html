{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'mixtures:list' %}">خلطات الأعلاف</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-blender"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                {% load crispy_forms_tags %}
                {% crispy form %}
            </div>
        </div>
    </div>
</div>

{% if mixture %}
<!-- معلومات إضافية للخلطة الموجودة -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator"></i>
                    القيم المحسوبة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>تكلفة الإنتاج:</strong><br>
                        <span class="currency">{{ mixture.cost_per_kg|floatformat:3 }} د.ع/كغم</span>
                    </div>
                    <div class="col-6">
                        <strong>هامش الربح:</strong><br>
                        <span class="{% if mixture.profit_margin_percentage > 20 %}text-success{% elif mixture.profit_margin_percentage > 10 %}text-warning{% else %}text-danger{% endif %}">
                            {{ mixture.profit_margin_percentage|floatformat:1 }}%
                        </span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>حالة الصيغة:</strong><br>
                        {% if mixture.is_formula_complete %}
                            <span class="status-badge status-active">مكتملة ({{ mixture.total_components_percentage|floatformat:1 }}%)</span>
                        {% else %}
                            <span class="status-badge status-pending">غير مكتملة ({{ mixture.total_components_percentage|floatformat:1 }}%)</span>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <strong>عدد المكونات:</strong><br>
                        {{ mixture.components.count }} مكون
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie"></i>
                    القيم الغذائية الحالية
                </h6>
            </div>
            <div class="card-body">
                {% with nutritional_values=mixture.calculate_nutritional_values %}
                <div class="row">
                    <div class="col-6">
                        <strong>البروتين الفعلي:</strong><br>
                        {{ nutritional_values.protein_percentage|floatformat:1 }}%
                        <small class="text-muted">(المستهدف: {{ mixture.target_protein_percentage|floatformat:1 }}%)</small>
                    </div>
                    <div class="col-6">
                        <strong>الطاقة الفعلية:</strong><br>
                        {{ nutritional_values.energy_kcal_per_kg|floatformat:0 }} ك.كالوري/كغم
                        <small class="text-muted">(المستهدف: {{ mixture.target_energy_kcal_per_kg|floatformat:0 }})</small>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>الألياف الفعلية:</strong><br>
                        {{ nutritional_values.fiber_percentage|floatformat:1 }}%
                        <small class="text-muted">(المستهدف: {{ mixture.target_fiber_percentage|floatformat:1 }}%)</small>
                    </div>
                    <div class="col-6">
                        <strong>إجمالي النسب:</strong><br>
                        {{ nutritional_values.total_percentage|floatformat:1 }}%
                    </div>
                </div>
                {% endwith %}
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <a href="{% url 'mixtures:detail' mixture.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{% url 'mixtures:component_add' mixture.pk %}" class="btn btn-outline-success">
                        <i class="fas fa-plus"></i>
                        إضافة مكون
                    </a>
                    <button type="button" class="btn btn-outline-info" onclick="calculateMixture()">
                        <i class="fas fa-calculator"></i>
                        إعادة حساب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'mixtures:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if mixture %}
<script>
function calculateMixture() {
    fetch('{% url "mixtures:calculate" mixture.pk %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إعادة حساب القيم بنجاح!');
            location.reload();
        } else {
            alert('حدث خطأ أثناء الحساب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الحساب');
    });
}
</script>
{% endif %}
{% endblock %}
