from django.contrib import admin
from .models import Invoice, InvoiceItem, Payment


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 0
    readonly_fields = ['total_price']


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ['created_at']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = [
        'invoice_number', 'customer', 'issue_date', 'due_date',
        'total_amount', 'paid_amount', 'remaining_amount_display', 'status'
    ]
    list_filter = ['status', 'issue_date', 'due_date']
    search_fields = ['invoice_number', 'customer__name', 'order__order_number']
    readonly_fields = [
        'invoice_number', 'issue_date', 'subtotal', 'tax_amount', 'discount_amount',
        'total_amount', 'paid_amount', 'created_at', 'updated_at'
    ]
    inlines = [InvoiceItemInline, PaymentInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('invoice_number', 'order', 'customer', 'status')
        }),
        ('التواريخ', {
            'fields': ('issue_date', 'due_date')
        }),
        ('المبالغ المالية', {
            'fields': (
                'subtotal', 'tax_rate', 'tax_amount',
                'discount_rate', 'discount_amount',
                'total_amount', 'paid_amount'
            )
        }),
        ('ملاحظات', {
            'fields': ('notes', 'terms_conditions'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def remaining_amount_display(self, obj):
        return f"{obj.remaining_amount:.3f} د.أ"
    remaining_amount_display.short_description = 'المبلغ المتبقي'

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = [
        'invoice', 'amount', 'payment_date', 'payment_method', 'created_by'
    ]
    list_filter = ['payment_method', 'payment_date']
    search_fields = ['invoice__invoice_number', 'reference_number']
    readonly_fields = ['created_at']

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
