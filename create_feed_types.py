#!/usr/bin/env python
"""
إنشاء أنواع أعلاف أساسية
"""
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
django.setup()

from mixtures.models import FeedType

def create_feed_types():
    """إنشاء أنواع أعلاف أساسية"""
    
    feed_types_data = [
        {
            'name': 'علف دجاج بياض',
            'target_animal': 'دجاج بياض',
            'description': 'علف مخصص للدجاج البياض لزيادة إنتاج البيض'
        },
        {
            'name': 'علف دجاج لاحم',
            'target_animal': 'دجاج لاحم',
            'description': 'علف مخصص للدجاج اللاحم لزيادة الوزن'
        },
        {
            'name': 'علف أبقار حلوب',
            'target_animal': 'أبقار حلوب',
            'description': 'علف مخصص للأبقار الحلوب لزيادة إنتاج الحليب'
        },
        {
            'name': 'علف أبقار تسمين',
            'target_animal': 'أبقار تسمين',
            'description': 'علف مخصص لتسمين الأبقار'
        },
        {
            'name': 'علف أغنام',
            'target_animal': 'أغنام',
            'description': 'علف مخصص للأغنام والماعز'
        },
        {
            'name': 'علف خيول',
            'target_animal': 'خيول',
            'description': 'علف مخصص للخيول'
        }
    ]
    
    created_count = 0
    for data in feed_types_data:
        feed_type, created = FeedType.objects.get_or_create(
            name=data['name'],
            defaults={
                'target_animal': data['target_animal'],
                'description': data['description']
            }
        )
        if created:
            created_count += 1
            print(f"✅ تم إنشاء نوع العلف: {feed_type.name}")
        else:
            print(f"ℹ️  نوع العلف موجود بالفعل: {feed_type.name}")
    
    print(f"\n🎉 تم إنشاء {created_count} نوع علف جديد")
    print(f"📊 إجمالي أنواع الأعلاف: {FeedType.objects.count()}")

if __name__ == '__main__':
    create_feed_types()
