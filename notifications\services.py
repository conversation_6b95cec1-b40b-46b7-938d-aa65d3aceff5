from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from .models import Notification, NotificationTemplate, NotificationSettings
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """خدمة إدارة الإشعارات"""
    
    @staticmethod
    def create_notification(user, title, message, notification_type='info', 
                          priority='medium', action_url=None, action_text=None, 
                          data=None, expires_at=None):
        """إنشاء إشعار جديد"""
        try:
            notification = Notification.objects.create(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                priority=priority,
                action_url=action_url,
                action_text=action_text,
                data=data or {},
                expires_at=expires_at
            )
            
            # إرسال الإشعار حسب إعدادات المستخدم
            NotificationService.send_notification(notification)
            
            return notification
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return None
    
    @staticmethod
    def create_from_template(template_name, user, context=None):
        """إنشاء إشعار من قالب"""
        try:
            template = NotificationTemplate.objects.get(name=template_name)
            rendered = template.render(context)
            
            return NotificationService.create_notification(
                user=user,
                title=rendered['title'],
                message=rendered['message'],
                notification_type=rendered['notification_type'],
                priority=rendered['priority']
            )
            
        except NotificationTemplate.DoesNotExist:
            logger.error(f"Notification template '{template_name}' not found")
            return None
        except Exception as e:
            logger.error(f"Error creating notification from template: {e}")
            return None
    
    @staticmethod
    def send_notification(notification):
        """إرسال الإشعار حسب إعدادات المستخدم"""
        try:
            settings_obj, created = NotificationSettings.objects.get_or_create(
                user=notification.user
            )
            
            # التحقق من الساعات الهادئة
            if NotificationService.is_quiet_hours(settings_obj):
                return
            
            # إرسال إشعار فوري (في المتصفح)
            if settings_obj.push_notifications:
                NotificationService.send_push_notification(notification)
            
            # إرسال بريد إلكتروني
            if settings_obj.email_notifications:
                NotificationService.send_email_notification(notification)
            
            # إرسال رسالة نصية (إذا كان متاحاً)
            if settings_obj.sms_notifications:
                NotificationService.send_sms_notification(notification)
                
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
    
    @staticmethod
    def is_quiet_hours(settings_obj):
        """التحقق من الساعات الهادئة"""
        current_time = timezone.now().time()
        start = settings_obj.quiet_hours_start
        end = settings_obj.quiet_hours_end
        
        if start <= end:
            return start <= current_time <= end
        else:  # يمتد عبر منتصف الليل
            return current_time >= start or current_time <= end
    
    @staticmethod
    def send_push_notification(notification):
        """إرسال إشعار فوري (WebSocket أو Server-Sent Events)"""
        # يمكن تطوير هذا لاحقاً باستخدام WebSocket أو SSE
        pass
    
    @staticmethod
    def send_email_notification(notification):
        """إرسال إشعار عبر البريد الإلكتروني"""
        try:
            if not notification.user.email:
                return
            
            subject = f"[مصنع الأعلاف] {notification.title}"
            
            # تحضير محتوى البريد
            context = {
                'notification': notification,
                'user': notification.user,
                'site_name': 'مصنع الأعلاف',
            }
            
            html_message = render_to_string(
                'notifications/email_notification.html', 
                context
            )
            plain_message = render_to_string(
                'notifications/email_notification.txt', 
                context
            )
            
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notification.user.email],
                html_message=html_message,
                fail_silently=False
            )
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
    
    @staticmethod
    def send_sms_notification(notification):
        """إرسال إشعار عبر الرسائل النصية"""
        # يمكن تطوير هذا لاحقاً باستخدام خدمة SMS
        pass
    
    @staticmethod
    def bulk_notify(users, title, message, **kwargs):
        """إرسال إشعار جماعي"""
        notifications = []
        for user in users:
            notification = NotificationService.create_notification(
                user=user,
                title=title,
                message=message,
                **kwargs
            )
            if notification:
                notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def notify_admins(title, message, **kwargs):
        """إشعار المديرين"""
        admin_users = User.objects.filter(is_staff=True)
        return NotificationService.bulk_notify(admin_users, title, message, **kwargs)
    
    @staticmethod
    def mark_all_as_read(user):
        """تحديد جميع إشعارات المستخدم كمقروءة"""
        return Notification.objects.filter(
            user=user, 
            is_read=False
        ).update(
            is_read=True, 
            read_at=timezone.now()
        )
    
    @staticmethod
    def get_unread_count(user):
        """عدد الإشعارات غير المقروءة"""
        return Notification.objects.filter(user=user, is_read=False).count()
    
    @staticmethod
    def cleanup_old_notifications(days=30):
        """تنظيف الإشعارات القديمة"""
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        deleted_count = Notification.objects.filter(
            created_at__lt=cutoff_date,
            is_read=True
        ).delete()[0]
        
        logger.info(f"Cleaned up {deleted_count} old notifications")
        return deleted_count


# دوال مساعدة للأحداث المختلفة
def notify_new_order(order):
    """إشعار بطلب جديد"""
    admin_users = User.objects.filter(is_staff=True)
    for user in admin_users:
        NotificationService.create_notification(
            user=user,
            title="طلب جديد",
            message=f"تم إنشاء طلب جديد رقم {order.order_number} من العميل {order.customer.name}",
            notification_type='order',
            priority='high',
            action_url=f"/orders/{order.pk}/",
            action_text="عرض الطلب",
            data={'order_id': order.pk}
        )


def notify_payment_received(payment):
    """إشعار بدفعة جديدة"""
    admin_users = User.objects.filter(is_staff=True)
    for user in admin_users:
        NotificationService.create_notification(
            user=user,
            title="دفعة جديدة",
            message=f"تم استلام دفعة بمبلغ {payment.amount} د.أ للفاتورة {payment.invoice.invoice_number}",
            notification_type='payment',
            priority='medium',
            action_url=f"/invoices/{payment.invoice.pk}/",
            action_text="عرض الفاتورة",
            data={'payment_id': payment.pk, 'invoice_id': payment.invoice.pk}
        )


def notify_low_stock(ingredient):
    """إشعار بنقص المخزون"""
    admin_users = User.objects.filter(is_staff=True)
    for user in admin_users:
        NotificationService.create_notification(
            user=user,
            title="تحذير: نقص في المخزون",
            message=f"المكون {ingredient.name} أصبح أقل من الحد الأدنى ({ingredient.current_stock} كغم)",
            notification_type='stock',
            priority='urgent',
            action_url=f"/ingredients/{ingredient.pk}/",
            action_text="عرض المكون",
            data={'ingredient_id': ingredient.pk}
        )


def notify_invoice_created(invoice):
    """إشعار بفاتورة جديدة"""
    admin_users = User.objects.filter(is_staff=True)
    for user in admin_users:
        NotificationService.create_notification(
            user=user,
            title="فاتورة جديدة",
            message=f"تم إنشاء فاتورة جديدة رقم {invoice.invoice_number} للعميل {invoice.customer.name}",
            notification_type='invoice',
            priority='medium',
            action_url=f"/invoices/{invoice.pk}/",
            action_text="عرض الفاتورة",
            data={'invoice_id': invoice.pk}
        )
