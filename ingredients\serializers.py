from rest_framework import serializers
from .models import Ingredient, IngredientCategory


class IngredientCategorySerializer(serializers.ModelSerializer):
    """مسلسل فئات المكونات"""
    
    ingredients_count = serializers.SerializerMethodField()
    
    class Meta:
        model = IngredientCategory
        fields = [
            'id', 'name', 'description', 'ingredients_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_ingredients_count(self, obj):
        """عدد المكونات في هذه الفئة"""
        return obj.ingredients.filter(is_active=True).count()


class IngredientListSerializer(serializers.ModelSerializer):
    """مسلسل قائمة المكونات (مبسط)"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    stock_status = serializers.CharField(read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Ingredient
        fields = [
            'id', 'name', 'category', 'category_name', 'protein_percentage',
            'energy_kcal_per_kg', 'cost_per_kg', 'current_stock',
            'minimum_stock', 'stock_status', 'is_low_stock', 'is_active'
        ]


class IngredientDetailSerializer(serializers.ModelSerializer):
    """مسلسل تفاصيل المكونات (كامل)"""
    
    category = IngredientCategorySerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True)
    stock_status = serializers.CharField(read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Ingredient
        fields = [
            'id', 'name', 'category', 'category_id', 'description',
            'protein_percentage', 'energy_kcal_per_kg', 'fiber_percentage',
            'fat_percentage', 'ash_percentage', 'moisture_percentage',
            'cost_per_kg', 'current_stock', 'minimum_stock',
            'stock_status', 'is_low_stock', 'supplier', 'notes',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """التحقق من صحة البيانات"""
        # التحقق من أن مجموع النسب المئوية لا يتجاوز 100%
        percentages = [
            data.get('protein_percentage', 0),
            data.get('fiber_percentage', 0),
            data.get('fat_percentage', 0),
            data.get('ash_percentage', 0),
            data.get('moisture_percentage', 0),
        ]
        
        total_percentage = sum(p for p in percentages if p is not None)
        if total_percentage > 100:
            raise serializers.ValidationError(
                'مجموع النسب المئوية لا يمكن أن يتجاوز 100%'
            )
        
        # التحقق من أن المخزون الحالي أكبر من أو يساوي الصفر
        current_stock = data.get('current_stock')
        if current_stock is not None and current_stock < 0:
            raise serializers.ValidationError(
                'المخزون الحالي لا يمكن أن يكون سالباً'
            )
        
        return data


class IngredientCreateUpdateSerializer(serializers.ModelSerializer):
    """مسلسل إنشاء وتحديث المكونات"""
    
    class Meta:
        model = Ingredient
        fields = [
            'name', 'category', 'description', 'protein_percentage',
            'energy_kcal_per_kg', 'fiber_percentage', 'fat_percentage',
            'ash_percentage', 'moisture_percentage', 'cost_per_kg',
            'current_stock', 'minimum_stock', 'supplier', 'notes', 'is_active'
        ]
    
    def validate(self, data):
        """التحقق من صحة البيانات"""
        # التحقق من أن مجموع النسب المئوية لا يتجاوز 100%
        percentages = [
            data.get('protein_percentage', 0),
            data.get('fiber_percentage', 0),
            data.get('fat_percentage', 0),
            data.get('ash_percentage', 0),
            data.get('moisture_percentage', 0),
        ]
        
        total_percentage = sum(p for p in percentages if p is not None)
        if total_percentage > 100:
            raise serializers.ValidationError({
                'non_field_errors': ['مجموع النسب المئوية لا يمكن أن يتجاوز 100%']
            })
        
        return data
