# تشغيل خادم Django
Write-Host "🚀 Starting Django Server..." -ForegroundColor Green
Write-Host "📁 Working Directory: $(Get-Location)" -ForegroundColor Yellow

# التحقق من وجود manage.py
if (Test-Path "manage.py") {
    Write-Host "✅ manage.py found" -ForegroundColor Green
} else {
    Write-Host "❌ manage.py not found!" -ForegroundColor Red
    exit 1
}

# التحقق من Django
try {
    python -c "import django; print('Django version:', django.get_version())"
    Write-Host "✅ Django is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Django not available!" -ForegroundColor Red
    exit 1
}

# تشغيل الخادم
Write-Host "🌐 Starting server on http://127.0.0.1:8000/" -ForegroundColor Cyan
Write-Host "🔑 Admin: http://127.0.0.1:8000/admin/" -ForegroundColor Cyan
Write-Host "👤 Username: admin | 🔐 Password: admin123456" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Magenta
Write-Host "-" * 50

python manage.py runserver 127.0.0.1:8000
