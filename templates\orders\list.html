{% extends 'base.html' %}

{% block title %}قائمة الطلبات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">الطلبات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-shopping-cart"></i>
                إدارة الطلبات
            </h1>
            <a href="{% url 'orders:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                طلب جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_orders }}</h4>
                        <p class="mb-0">إجمالي الطلبات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.pending_orders }}</h4>
                        <p class="mb-0">في الانتظار</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.confirmed_orders }}</h4>
                        <p class="mb-0">مؤكدة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.delivered_orders }}</h4>
                        <p class="mb-0">تم التسليم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-search"></i>
            البحث والتصفية
        </h6>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                {{ form.search.label_tag }}
                {{ form.search }}
            </div>
            <div class="col-md-2">
                {{ form.status.label_tag }}
                {{ form.status }}
            </div>
            <div class="col-md-2">
                {{ form.payment_status.label_tag }}
                {{ form.payment_status }}
            </div>
            <div class="col-md-2">
                {{ form.customer.label_tag }}
                {{ form.customer }}
            </div>
            <div class="col-md-1">
                {{ form.date_from.label_tag }}
                {{ form.date_from }}
            </div>
            <div class="col-md-1">
                {{ form.date_to.label_tag }}
                {{ form.date_to }}
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
                <a href="{% url 'orders:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول الطلبات -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة الطلبات ({{ page_obj.paginator.count }} طلب)
        </h6>
    </div>
    <div class="card-body">
        {% if orders %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>تاريخ الطلب</th>
                        <th>التاريخ المطلوب</th>
                        <th>حالة الطلب</th>
                        <th>حالة الدفع</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                    <tr>
                        <td>
                            <a href="{% url 'orders:detail' order.pk %}" class="text-decoration-none">
                                <strong>{{ order.order_number }}</strong>
                            </a>
                        </td>
                        <td>
                            <div>
                                <strong>{{ order.customer.name }}</strong>
                                {% if order.customer.company_name %}
                                <br><small class="text-muted">{{ order.customer.company_name }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ order.order_date|date:"Y/m/d H:i" }}</td>
                        <td>{{ order.required_date|date:"Y/m/d" }}</td>
                        <td>
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">مؤكد</span>
                            {% elif order.status == 'in_production' %}
                                <span class="badge bg-primary">قيد الإنتاج</span>
                            {% elif order.status == 'ready' %}
                                <span class="badge bg-secondary">جاهز</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.payment_status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوع</span>
                            {% elif order.payment_status == 'partial' %}
                                <span class="badge bg-warning">مدفوع جزئياً</span>
                            {% elif order.payment_status == 'paid' %}
                                <span class="badge bg-success">مدفوع بالكامل</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong class="currency">{{ order.total_amount|floatformat:3 }} د.ع</strong>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'orders:detail' order.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'orders:edit' order.pk %}" class="btn btn-sm btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'orders:invoice' order.pk %}" class="btn btn-sm btn-outline-info" title="فاتورة">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح الطلبات">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}">السابق</a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد طلبات</h5>
            <p class="text-muted">لم يتم العثور على أي طلبات تطابق معايير البحث</p>
            <a href="{% url 'orders:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة طلب جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
