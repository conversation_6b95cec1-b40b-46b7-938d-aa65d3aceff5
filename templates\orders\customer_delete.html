{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:customers' %}">العملاء</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:customer_detail' customer.pk %}">{{ customer.name }}</a></li>
        <li class="breadcrumb-item active">حذف العميل</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد حذف العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات العميل:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الاسم:</strong> {{ customer.name }}</li>
                            <li><strong>البريد الإلكتروني:</strong> {{ customer.email|default:"غير محدد" }}</li>
                            <li><strong>الهاتف:</strong> {{ customer.phone|default:"غير محدد" }}</li>
                            <li><strong>العنوان:</strong> {{ customer.address|default:"غير محدد" }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الإحصائيات:</h6>
                        <ul class="list-unstyled">
                            <li><strong>عدد الطلبات:</strong> {{ orders_count }}</li>
                            <li><strong>الحالة:</strong> 
                                {% if customer.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </li>
                            <li><strong>تاريخ التسجيل:</strong> {{ customer.created_at|date:"d/m/Y" }}</li>
                        </ul>
                    </div>
                </div>

                {% if orders_count > 0 %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> 
                    هذا العميل لديه {{ orders_count }} طلب مرتبط به. 
                    سيتم إلغاء تفعيل العميل بدلاً من حذفه نهائياً للحفاظ على سجل الطلبات.
                </div>
                {% else %}
                <div class="alert alert-danger">
                    <i class="fas fa-trash"></i>
                    <strong>تحذير:</strong> 
                    سيتم حذف العميل نهائياً من قاعدة البيانات لعدم وجود طلبات مرتبطة به.
                </div>
                {% endif %}

                <form method="post" class="mt-4">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{% url 'orders:customer_detail' customer.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                إلغاء
                            </a>
                        </div>
                        <div>
                            {% if orders_count > 0 %}
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-user-slash"></i>
                                    إلغاء تفعيل العميل
                                </button>
                            {% else %}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i>
                                    حذف العميل نهائياً
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if orders_count > 0 %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shopping-cart"></i>
                    آخر الطلبات ({{ orders_count }} طلب)
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in customer.orders.all|slice:":5" %}
                            <tr>
                                <td>{{ order.order_number }}</td>
                                <td>{{ order.order_date|date:"d/m/Y" }}</td>
                                <td>{{ order.total_amount|floatformat:3 }} د.أ</td>
                                <td>
                                    <span class="badge bg-{% if order.status == 'completed' %}success{% elif order.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if orders_count > 5 %}
                <small class="text-muted">عرض 5 من أصل {{ orders_count }} طلب</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// تأكيد إضافي قبل الحذف
document.querySelector('form').addEventListener('submit', function(e) {
    {% if orders_count > 0 %}
    const confirmed = confirm('هل أنت متأكد من إلغاء تفعيل هذا العميل؟');
    {% else %}
    const confirmed = confirm('هل أنت متأكد من حذف هذا العميل نهائياً؟ لا يمكن التراجع عن هذا الإجراء!');
    {% endif %}
    
    if (!confirmed) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
