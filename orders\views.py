from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Order, Customer, OrderItem
from .forms import OrderForm, CustomerForm, OrderItemForm, OrderSearchForm, CustomerSearchForm
from mixtures.models import FeedMixture


@login_required
def order_list(request):
    """قائمة الطلبات مع البحث والتصفية"""
    form = OrderSearchForm(request.GET)
    orders = Order.objects.select_related('customer', 'created_by').order_by('-order_date')

    # تطبيق الفلاتر
    if form.is_valid():
        search = form.cleaned_data.get('search')
        status = form.cleaned_data.get('status')
        payment_status = form.cleaned_data.get('payment_status')
        customer = form.cleaned_data.get('customer')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            orders = orders.filter(
                Q(order_number__icontains=search) |
                Q(customer__name__icontains=search) |
                Q(customer__company_name__icontains=search)
            )

        if status:
            orders = orders.filter(status=status)

        if payment_status:
            orders = orders.filter(payment_status=payment_status)

        if customer:
            orders = orders.filter(customer=customer)

        if date_from:
            orders = orders.filter(order_date__date__gte=date_from)

        if date_to:
            orders = orders.filter(order_date__date__lte=date_to)

    # التصفح
    paginator = Paginator(orders, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات سريعة
    stats = {
        'total_orders': orders.count(),
        'pending_orders': orders.filter(status='pending').count(),
        'confirmed_orders': orders.filter(status='confirmed').count(),
        'delivered_orders': orders.filter(status='delivered').count(),
    }

    context = {
        'orders': page_obj,
        'form': form,
        'page_obj': page_obj,
        'stats': stats
    }
    return render(request, 'orders/list.html', context)


@login_required
def order_detail(request, pk):
    """تفاصيل الطلب"""
    order = get_object_or_404(Order, pk=pk)
    items = order.items.select_related('mixture').all()

    context = {
        'order': order,
        'items': items
    }
    return render(request, 'orders/detail.html', context)


@login_required
def order_add(request):
    """إضافة طلب جديد"""
    if request.method == 'POST':
        form = OrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user

            # توليد رقم الطلب
            today = timezone.now().date()
            today_orders = Order.objects.filter(order_date__date=today).count()
            order.order_number = f"ORD-{today.strftime('%Y%m%d')}-{today_orders + 1:03d}"

            order.save()
            messages.success(request, f'تم إضافة الطلب "{order.order_number}" بنجاح')
            return redirect('orders:detail', pk=order.pk)
    else:
        form = OrderForm()

    context = {
        'form': form,
        'title': 'إضافة طلب جديد'
    }
    return render(request, 'orders/form.html', context)


@login_required
def order_edit(request, pk):
    """تعديل الطلب"""
    order = get_object_or_404(Order, pk=pk)

    if request.method == 'POST':
        form = OrderForm(request.POST, instance=order)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الطلب "{order.order_number}" بنجاح')
            return redirect('orders:detail', pk=pk)
    else:
        form = OrderForm(instance=order)

    context = {
        'form': form,
        'order': order,
        'title': f'تعديل الطلب: {order.order_number}'
    }
    return render(request, 'orders/form.html', context)


@login_required
def order_delete(request, pk):
    """حذف الطلب"""
    order = get_object_or_404(Order, pk=pk)

    if request.method == 'POST':
        order_number = order.order_number
        order.delete()
        messages.success(request, f'تم حذف الطلب "{order_number}" بنجاح')
        return redirect('orders:list')

    context = {
        'order': order,
        'title': f'حذف الطلب: {order.order_number}'
    }
    return render(request, 'orders/delete.html', context)


@login_required
def order_invoice(request, pk):
    """فاتورة الطلب"""
    order = get_object_or_404(Order, pk=pk)
    items = order.items.select_related('mixture').all()

    context = {
        'order': order,
        'items': items
    }
    return render(request, 'orders/invoice.html', context)


@login_required
def customer_list(request):
    """قائمة العملاء مع البحث والتصفية"""
    form = CustomerSearchForm(request.GET)
    customers = Customer.objects.all().order_by('name')

    # تطبيق الفلاتر
    if form.is_valid():
        search = form.cleaned_data.get('search')
        city = form.cleaned_data.get('city')
        is_active = form.cleaned_data.get('is_active')

        if search:
            customers = customers.filter(
                Q(name__icontains=search) |
                Q(company_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        if city:
            customers = customers.filter(city__icontains=city)

        if is_active == 'true':
            customers = customers.filter(is_active=True)
        elif is_active == 'false':
            customers = customers.filter(is_active=False)

    # التصفح
    paginator = Paginator(customers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات سريعة
    stats = {
        'total_customers': customers.count(),
        'active_customers': customers.filter(is_active=True).count(),
        'inactive_customers': customers.filter(is_active=False).count(),
    }

    context = {
        'customers': page_obj,
        'form': form,
        'page_obj': page_obj,
        'stats': stats
    }
    return render(request, 'orders/customers.html', context)


@login_required
def customer_add(request):
    """إضافة عميل جديد"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save()
            messages.success(request, f'تم إضافة العميل "{customer.name}" بنجاح')
            return redirect('orders:customer_detail', pk=customer.pk)
    else:
        form = CustomerForm()

    context = {
        'form': form,
        'title': 'إضافة عميل جديد'
    }
    return render(request, 'orders/customer_form.html', context)


@login_required
def customer_detail(request, pk):
    """تفاصيل العميل"""
    customer = get_object_or_404(Customer, pk=pk)
    orders = customer.orders.order_by('-order_date')[:10]

    # إحصائيات العميل
    total_orders = customer.orders.count()
    total_amount = sum(order.total_amount for order in customer.orders.all())
    pending_orders = customer.orders.filter(status='pending').count()

    context = {
        'customer': customer,
        'orders': orders,
        'stats': {
            'total_orders': total_orders,
            'total_amount': total_amount,
            'pending_orders': pending_orders,
        }
    }
    return render(request, 'orders/customer_detail.html', context)


@login_required
def customer_edit(request, pk):
    """تعديل بيانات العميل"""
    customer = get_object_or_404(Customer, pk=pk)

    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            customer = form.save()
            messages.success(request, f'تم تحديث بيانات العميل "{customer.name}" بنجاح')
            return redirect('orders:customer_detail', pk=customer.pk)
    else:
        form = CustomerForm(instance=customer)

    context = {
        'form': form,
        'customer': customer,
        'title': f'تعديل بيانات العميل: {customer.name}'
    }
    return render(request, 'orders/customer_form.html', context)


@login_required
def customer_delete(request, pk):
    """حذف العميل"""
    customer = get_object_or_404(Customer, pk=pk)

    # التحقق من وجود طلبات للعميل
    orders_count = customer.orders.count()

    if request.method == 'POST':
        if orders_count > 0:
            # إذا كان للعميل طلبات، قم بإلغاء تفعيله بدلاً من الحذف
            customer.is_active = False
            customer.save()
            messages.warning(request, f'تم إلغاء تفعيل العميل "{customer.name}" لوجود طلبات مرتبطة به')
        else:
            # إذا لم يكن للعميل طلبات، احذفه نهائياً
            customer_name = customer.name
            customer.delete()
            messages.success(request, f'تم حذف العميل "{customer_name}" نهائياً')

        return redirect('orders:customers')

    context = {
        'customer': customer,
        'orders_count': orders_count,
        'title': f'حذف العميل: {customer.name}'
    }
    return render(request, 'orders/customer_delete.html', context)


@login_required
def order_item_add(request, order_pk):
    """إضافة عنصر للطلب"""
    order = get_object_or_404(Order, pk=order_pk)

    if request.method == 'POST':
        form = OrderItemForm(request.POST, order=order)
        if form.is_valid():
            item = form.save(commit=False)
            item.order = order
            item.save()
            messages.success(request, f'تم إضافة "{item.mixture.name}" للطلب بنجاح')
            return redirect('orders:detail', pk=order.pk)
    else:
        form = OrderItemForm(order=order)

    context = {
        'form': form,
        'order': order,
        'title': f'إضافة عنصر للطلب: {order.order_number}'
    }
    return render(request, 'orders/item_form.html', context)


@login_required
def order_item_edit(request, order_pk, item_pk):
    """تعديل عنصر في الطلب"""
    order = get_object_or_404(Order, pk=order_pk)
    item = get_object_or_404(OrderItem, pk=item_pk, order=order)

    if request.method == 'POST':
        form = OrderItemForm(request.POST, instance=item, order=order)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث "{item.mixture.name}" بنجاح')
            return redirect('orders:detail', pk=order.pk)
    else:
        form = OrderItemForm(instance=item, order=order)

    context = {
        'form': form,
        'order': order,
        'item': item,
        'title': f'تعديل العنصر: {item.mixture.name}'
    }
    return render(request, 'orders/item_form.html', context)


@login_required
def order_item_delete(request, order_pk, item_pk):
    """حذف عنصر من الطلب"""
    order = get_object_or_404(Order, pk=order_pk)
    item = get_object_or_404(OrderItem, pk=item_pk, order=order)

    if request.method == 'POST':
        mixture_name = item.mixture.name
        item.delete()
        messages.success(request, f'تم حذف "{mixture_name}" من الطلب')
        return redirect('orders:detail', pk=order.pk)

    context = {
        'order': order,
        'item': item,
        'title': f'حذف العنصر: {item.mixture.name}'
    }
    return render(request, 'orders/item_delete.html', context)
