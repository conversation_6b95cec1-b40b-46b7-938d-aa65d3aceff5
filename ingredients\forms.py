from django import forms
from django.utils.translation import gettext_lazy as _
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import Ingredient, IngredientCategory


class IngredientCategoryForm(forms.ModelForm):
    """نموذج إضافة/تعديل فئة المكونات"""
    
    class Meta:
        model = IngredientCategory
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: الحبوب'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف مختصر للفئة...'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        self.helper.layout = Layout(
            Fieldset(
                _('معلومات الفئة'),
                'name',
                'description',
            ),
            FormActions(
                Submit('submit', _('حفظ'), css_class='btn btn-primary'),
                HTML('<a href="{% url "ingredients:categories" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )


class IngredientForm(forms.ModelForm):
    """نموذج إضافة/تعديل المكونات"""
    
    class Meta:
        model = Ingredient
        fields = [
            'name', 'category', 'description', 'protein_percentage',
            'energy_kcal_per_kg', 'fiber_percentage', 'fat_percentage',
            'ash_percentage', 'moisture_percentage', 'cost_per_kg',
            'current_stock', 'minimum_stock', 'supplier', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: ذرة صفراء'
            }),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'وصف المكون...'
            }),
            'protein_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'energy_kcal_per_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'fiber_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'fat_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'ash_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'moisture_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'cost_per_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'current_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'minimum_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'supplier': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المورد...'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية...'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        self.helper.layout = Layout(
            Fieldset(
                _('المعلومات الأساسية'),
                Row(
                    Column('name', css_class='col-md-8'),
                    Column('category', css_class='col-md-4'),
                ),
                'description',
                'is_active',
            ),
            Fieldset(
                _('القيم الغذائية (%)'),
                Row(
                    Column('protein_percentage', css_class='col-md-6'),
                    Column('energy_kcal_per_kg', css_class='col-md-6'),
                ),
                Row(
                    Column('fiber_percentage', css_class='col-md-4'),
                    Column('fat_percentage', css_class='col-md-4'),
                    Column('ash_percentage', css_class='col-md-4'),
                ),
                'moisture_percentage',
            ),
            Fieldset(
                _('معلومات التكلفة والمخزون'),
                Row(
                    Column('cost_per_kg', css_class='col-md-4'),
                    Column('current_stock', css_class='col-md-4'),
                    Column('minimum_stock', css_class='col-md-4'),
                ),
                'supplier',
            ),
            Fieldset(
                _('ملاحظات'),
                'notes',
            ),
            FormActions(
                Submit('submit', _('حفظ'), css_class='btn btn-primary'),
                HTML('<a href="{% url "ingredients:list" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        
        # التحقق من أن مجموع النسب المئوية لا يتجاوز 100%
        percentages = [
            cleaned_data.get('protein_percentage', 0),
            cleaned_data.get('fiber_percentage', 0),
            cleaned_data.get('fat_percentage', 0),
            cleaned_data.get('ash_percentage', 0),
            cleaned_data.get('moisture_percentage', 0),
        ]
        
        total_percentage = sum(p for p in percentages if p is not None)
        if total_percentage > 100:
            raise forms.ValidationError(
                _('مجموع النسب المئوية لا يمكن أن يتجاوز 100%')
            )
        
        # التحقق من أن المخزون الحالي أكبر من أو يساوي الصفر
        current_stock = cleaned_data.get('current_stock')
        if current_stock is not None and current_stock < 0:
            raise forms.ValidationError(
                _('المخزون الحالي لا يمكن أن يكون سالباً')
            )
        
        return cleaned_data


class IngredientSearchForm(forms.Form):
    """نموذج البحث في المكونات"""
    
    search = forms.CharField(
        label=_('البحث'),
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في أسماء المكونات...'
        })
    )
    
    category = forms.ModelChoiceField(
        label=_('الفئة'),
        queryset=IngredientCategory.objects.all(),
        required=False,
        empty_label=_('جميع الفئات'),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    stock_status = forms.ChoiceField(
        label=_('حالة المخزون'),
        choices=[
            ('', _('جميع الحالات')),
            ('available', _('متوفر')),
            ('low', _('مخزون منخفض')),
            ('out', _('نفد المخزون')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_active = forms.ChoiceField(
        label=_('الحالة'),
        choices=[
            ('', _('الكل')),
            ('true', _('نشط')),
            ('false', _('غير نشط')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'row g-3'
        self.helper.disable_csrf = True
        
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='col-md-4'),
                Column('category', css_class='col-md-2'),
                Column('stock_status', css_class='col-md-2'),
                Column('is_active', css_class='col-md-2'),
                Column(
                    Submit('submit', _('بحث'), css_class='btn btn-primary'),
                    css_class='col-md-2 d-flex align-items-end'
                ),
            )
        )
