{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:customers' %}">العملاء</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label_tag }}
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger">{{ form.name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.company_name.label_tag }}
                                {{ form.company_name }}
                                {% if form.company_name.errors %}
                                    <div class="text-danger">{{ form.company_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.phone.label_tag }}
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">{{ form.phone.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label_tag }}
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">{{ form.email.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العنوان -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                {{ form.address.label_tag }}
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger">{{ form.address.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.city.label_tag }}
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger">{{ form.city.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات مالية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.credit_limit.label_tag }}
                                {{ form.credit_limit }}
                                {% if form.credit_limit.errors %}
                                    <div class="text-danger">{{ form.credit_limit.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">الحد الأقصى للائتمان المسموح للعميل</small>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        {{ form.notes.label_tag }}
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:customers' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if customer %}تحديث العميل{% else %}إضافة العميل{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
