from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.db.models import Sum, Count, Avg
from .models import Customer, Order, OrderItem


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def customer_list_create(request):
    """قائمة وإنشاء العملاء"""
    if request.method == 'GET':
        customers = Customer.objects.filter(is_active=True)
        data = []
        for customer in customers:
            data.append({
                'id': customer.id,
                'name': customer.name,
                'company_name': customer.company_name,
                'phone': customer.phone,
                'email': customer.email,
                'city': customer.city,
                'credit_limit': float(customer.credit_limit),
                'current_balance': float(customer.current_balance),
                'available_credit': float(customer.available_credit)
            })
        return Response(data)
    
    elif request.method == 'POST':
        # إنشاء عميل جديد - سيتم تطويره لاحقاً
        return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def customer_detail(request, pk):
    """تفاصيل العميل"""
    try:
        customer = Customer.objects.get(pk=pk)
    except Customer.DoesNotExist:
        return Response({'error': 'العميل غير موجود'}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        return Response({
            'id': customer.id,
            'name': customer.name,
            'company_name': customer.company_name,
            'phone': customer.phone,
            'email': customer.email,
            'address': customer.address,
            'city': customer.city,
            'credit_limit': float(customer.credit_limit),
            'current_balance': float(customer.current_balance),
            'available_credit': float(customer.available_credit),
            'notes': customer.notes,
            'is_active': customer.is_active,
            'created_at': customer.created_at,
            'updated_at': customer.updated_at
        })
    
    # تحديث وحذف العميل - سيتم تطويرهما لاحقاً
    return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def order_list_create(request):
    """قائمة وإنشاء الطلبات"""
    if request.method == 'GET':
        orders = Order.objects.select_related('customer').order_by('-created_at')[:20]
        data = []
        for order in orders:
            data.append({
                'id': order.id,
                'order_number': order.order_number,
                'customer': {
                    'id': order.customer.id,
                    'name': order.customer.name,
                    'company_name': order.customer.company_name
                },
                'order_date': order.order_date,
                'required_date': order.required_date,
                'status': order.status,
                'payment_status': order.payment_status,
                'total_amount': float(order.total_amount),
                'paid_amount': float(order.paid_amount),
                'remaining_amount': float(order.remaining_amount)
            })
        return Response(data)
    
    elif request.method == 'POST':
        # إنشاء طلب جديد - سيتم تطويره لاحقاً
        return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def order_detail(request, pk):
    """تفاصيل الطلب"""
    try:
        order = Order.objects.select_related('customer').get(pk=pk)
    except Order.DoesNotExist:
        return Response({'error': 'الطلب غير موجود'}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        items = order.items.select_related('mixture').all()
        items_data = []
        for item in items:
            items_data.append({
                'id': item.id,
                'mixture': {
                    'id': item.mixture.id,
                    'name': item.mixture.name
                },
                'quantity_kg': float(item.quantity_kg),
                'unit_price': float(item.unit_price),
                'total_price': float(item.total_price)
            })
        
        return Response({
            'id': order.id,
            'order_number': order.order_number,
            'customer': {
                'id': order.customer.id,
                'name': order.customer.name,
                'company_name': order.customer.company_name
            },
            'order_date': order.order_date,
            'required_date': order.required_date,
            'delivery_date': order.delivery_date,
            'status': order.status,
            'payment_status': order.payment_status,
            'subtotal': float(order.subtotal),
            'discount_percentage': float(order.discount_percentage),
            'discount_amount': float(order.discount_amount),
            'tax_percentage': float(order.tax_percentage),
            'tax_amount': float(order.tax_amount),
            'total_amount': float(order.total_amount),
            'paid_amount': float(order.paid_amount),
            'remaining_amount': float(order.remaining_amount),
            'total_weight': float(order.total_weight),
            'items': items_data,
            'notes': order.notes,
            'delivery_address': order.delivery_address
        })
    
    # تحديث وحذف الطلب - سيتم تطويرهما لاحقاً
    return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def order_stats(request):
    """إحصائيات الطلبات"""
    total_orders = Order.objects.count()
    total_customers = Customer.objects.filter(is_active=True).count()
    
    # إحصائيات حالة الطلبات
    pending_orders = Order.objects.filter(status='pending').count()
    confirmed_orders = Order.objects.filter(status='confirmed').count()
    delivered_orders = Order.objects.filter(status='delivered').count()
    
    # إحصائيات الدفع
    unpaid_orders = Order.objects.filter(payment_status='unpaid').count()
    partial_paid_orders = Order.objects.filter(payment_status='partial').count()
    paid_orders = Order.objects.filter(payment_status='paid').count()
    
    # إحصائيات مالية
    total_revenue = Order.objects.aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    
    total_paid = Order.objects.aggregate(
        total=Sum('paid_amount')
    )['total'] or 0
    
    avg_order_value = Order.objects.aggregate(
        avg=Avg('total_amount')
    )['avg'] or 0
    
    return Response({
        'total_orders': total_orders,
        'total_customers': total_customers,
        'order_status': {
            'pending': pending_orders,
            'confirmed': confirmed_orders,
            'delivered': delivered_orders
        },
        'payment_status': {
            'unpaid': unpaid_orders,
            'partial': partial_paid_orders,
            'paid': paid_orders
        },
        'financial_stats': {
            'total_revenue': round(float(total_revenue), 3),
            'total_paid': round(float(total_paid), 3),
            'outstanding_amount': round(float(total_revenue - total_paid), 3),
            'average_order_value': round(float(avg_order_value), 3)
        }
    })
