from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from ingredients.models import Ingredient
from mixtures.models import FeedMixture
from orders.models import Order, Customer
from .advanced_search import search_engine


@login_required
def dashboard(request):
    """لوحة التحكم الرئيسية المتقدمة"""
    from .widgets import DashboardManager
    from django.db.models import Sum

    # استخدام نظام الـ widgets الجديد
    dashboard_manager = DashboardManager()
    widgets = dashboard_manager.get_all_widgets()

    # إحصائيات سريعة للعرض في الأعلى
    stats = {
        'total_ingredients': Ingredient.objects.filter(is_active=True).count(),
        'total_mixtures': FeedMixture.objects.filter(is_active=True).count(),
        'total_customers': Customer.objects.filter(is_active=True).count(),
        'pending_orders': Order.objects.filter(status='pending').count(),
        'total_revenue': Order.objects.aggregate(total=Sum('total_amount'))['total'] or 0,
    }

    # الطلبات الحديثة (للعرض السريع)
    recent_orders = Order.objects.select_related('customer').order_by('-created_at')[:3]

    context = {
        'widgets': widgets,
        'stats': stats,
        'recent_orders': recent_orders,
    }

    return render(request, 'core/dashboard_advanced.html', context)


@login_required
def advanced_search(request):
    """البحث المتقدم"""
    query = request.GET.get('q', '').strip()
    model_filter = request.GET.get('model', '')

    results = []
    suggestions = []

    if query:
        if model_filter:
            # البحث في نموذج محدد
            results = search_engine.search_model(model_filter, query, limit=20)
        else:
            # البحث في جميع النماذج
            results = search_engine.search_all(query, limit=50)

    # الحصول على اقتراحات
    if len(query) >= 2:
        suggestions = search_engine.get_search_suggestions(query)

    context = {
        'query': query,
        'results': results,
        'suggestions': suggestions,
        'model_filter': model_filter,
        'searchable_models': search_engine.searchable_models,
        'total_results': len(results)
    }

    return render(request, 'core/search_results.html', context)


def search_api(request):
    """API للبحث السريع (AJAX)"""
    query = request.GET.get('q', '').strip()
    limit = int(request.GET.get('limit', 10))

    if not query or len(query) < 2:
        return JsonResponse({'results': [], 'suggestions': []})

    # البحث
    results = search_engine.search_all(query, limit=limit)

    # الاقتراحات
    suggestions = search_engine.get_search_suggestions(query, limit=5)

    return JsonResponse({
        'results': results,
        'suggestions': suggestions,
        'total': len(results)
    })


@login_required
def search_view(request):
    """صفحة البحث المتقدم"""
    from .search import AdvancedSearch, SearchFilters

    query = request.GET.get('q', '')
    search_type = request.GET.get('type', 'all')

    # جمع الفلاتر من الطلب
    filters = {}
    if request.GET.get('category'):
        filters['category'] = request.GET.get('category')
    if request.GET.get('status'):
        filters['status'] = request.GET.get('status')
    if request.GET.get('is_active'):
        filters['is_active'] = request.GET.get('is_active') == 'true'
    if request.GET.get('date_from'):
        filters['date_from'] = request.GET.get('date_from')
    if request.GET.get('date_to'):
        filters['date_to'] = request.GET.get('date_to')

    # تنفيذ البحث
    search = AdvancedSearch(query, search_type, filters)
    results = search.search()

    # الحصول على الفلاتر المتاحة
    available_filters = {
        'ingredients': SearchFilters.get_ingredient_filters(),
        'mixtures': SearchFilters.get_mixture_filters(),
        'orders': SearchFilters.get_order_filters(),
        'invoices': SearchFilters.get_invoice_filters(),
    }

    context = {
        'results': results,
        'query': query,
        'search_type': search_type,
        'filters': filters,
        'available_filters': available_filters,
    }

    return render(request, 'core/search_results.html', context)


@login_required
def search_suggestions(request):
    """API للحصول على اقتراحات البحث"""
    from django.http import JsonResponse
    from .search import SearchSuggestions

    query = request.GET.get('q', '')
    suggestions = SearchSuggestions.get_suggestions(query)

    return JsonResponse({'suggestions': suggestions})
