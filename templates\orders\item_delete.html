{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:detail' order.pk %}">{{ order.order_number }}</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-warning"></i> تحذير</h6>
                    <p class="mb-0">هل أنت متأكد من حذف هذا العنصر من الطلب؟ هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                
                <!-- معلومات العنصر -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات العنصر المراد حذفه</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>نوع الخلطة:</strong> {{ item.mixture.name }}</p>
                                <p><strong>نوع العلف:</strong> {{ item.mixture.feed_type.name }}</p>
                                <p><strong>الكمية:</strong> {{ item.quantity_kg|floatformat:2 }} كغم</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>سعر الوحدة:</strong> <span class="currency">{{ item.unit_price|floatformat:3 }} د.ع/كغم</span></p>
                                <p><strong>المبلغ الإجمالي:</strong> <span class="currency">{{ item.total_price|floatformat:3 }} د.ع</span></p>
                                {% if item.notes %}
                                <p><strong>ملاحظات:</strong> {{ item.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الطلب -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">تأثير الحذف على الطلب</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>رقم الطلب:</strong> {{ order.order_number }}</p>
                                <p><strong>العميل:</strong> {{ order.customer.name }}</p>
                                <p><strong>عدد العناصر الحالي:</strong> {{ order.items.count }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>المبلغ الحالي:</strong> <span class="currency">{{ order.total_amount|floatformat:3 }} د.ع</span></p>
                                <p><strong>المبلغ بعد الحذف:</strong> 
                                    <span class="currency text-info">{{ order.total_amount|add:item.total_price|floatformat:3 }} د.ع</span>
                                </p>
                                <p><strong>الفرق:</strong> 
                                    <span class="currency text-danger">-{{ item.total_price|floatformat:3 }} د.ع</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:detail' order.pk %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
