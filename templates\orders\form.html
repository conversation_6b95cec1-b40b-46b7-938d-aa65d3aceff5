{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.customer.label_tag }}
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger">{{ form.customer.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.required_date.label_tag }}
                                {{ form.required_date }}
                                {% if form.required_date.errors %}
                                    <div class="text-danger">{{ form.required_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.discount_percentage.label_tag }}
                                {{ form.discount_percentage }}
                                {% if form.discount_percentage.errors %}
                                    <div class="text-danger">{{ form.discount_percentage.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">اتركه فارغاً إذا لم يكن هناك خصم</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.delivery_address.label_tag }}
                        {{ form.delivery_address }}
                        {% if form.delivery_address.errors %}
                            <div class="text-danger">{{ form.delivery_address.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label_tag }}
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if order %}تحديث الطلب{% else %}إضافة الطلب{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {% if order %}
        <!-- معلومات إضافية للطلب الموجود -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم الطلب:</strong> {{ order.order_number }}</p>
                        <p><strong>تاريخ الإنشاء:</strong> {{ order.order_date|date:"Y/m/d H:i" }}</p>
                        <p><strong>أنشئ بواسطة:</strong> {{ order.created_by.get_full_name|default:order.created_by.username }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>حالة الطلب:</strong>
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">مؤكد</span>
                            {% elif order.status == 'in_production' %}
                                <span class="badge bg-primary">قيد الإنتاج</span>
                            {% elif order.status == 'ready' %}
                                <span class="badge bg-secondary">جاهز</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </p>
                        <p><strong>المبلغ الإجمالي:</strong> <span class="currency">{{ order.total_amount|floatformat:3 }} د.ع</span></p>
                        <p><strong>عدد العناصر:</strong> {{ order.items.count }}</p>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="{% url 'orders:detail' order.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i>
                        عرض تفاصيل الطلب
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
