# Generated by Django 5.2.1 on 2025-06-03 21:31

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0002_update_currency_to_jod'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(editable=False, max_length=20, unique=True, verbose_name='رقم الفاتورة')),
                ('issue_date', models.DateField(auto_now_add=True, verbose_name='تاريخ الإصدار')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('subtotal', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المجموع الفرعي (دينار أردني)')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='معدل الضريبة %')),
                ('tax_amount', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ الضريبة (دينار أردني)')),
                ('discount_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='معدل الخصم %')),
                ('discount_amount', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ الخصم (دينار أردني)')),
                ('total_amount', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ الإجمالي (دينار أردني)')),
                ('paid_amount', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ المدفوع (دينار أردني)')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='حالة الفاتورة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('terms_conditions', models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invoices', to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='orders.customer', verbose_name='العميل')),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='invoice', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=200, verbose_name='الوصف')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الوحدة (دينار أردني)')),
                ('total_price', models.DecimalField(decimal_places=3, editable=False, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='السعر الإجمالي (دينار أردني)')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='invoices.invoice', verbose_name='الفاتورة')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة',
                'verbose_name_plural': 'عناصر الفاتورة',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=3, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ (دينار أردني)')),
                ('payment_date', models.DateField(verbose_name='تاريخ الدفع')),
                ('payment_method', models.CharField(choices=[('cash', 'نقداً'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان'), ('other', 'أخرى')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='invoices.invoice', verbose_name='الفاتورة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
            },
        ),
    ]
