// Progressive Web App (PWA) functionality

// Service Worker registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/static/js/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed: ', err);
            });
    });
}

// Install prompt
let deferredPrompt;
const installButton = document.getElementById('install-button');

window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent Chrome 67 and earlier from automatically showing the prompt
    e.preventDefault();
    // Stash the event so it can be triggered later
    deferredPrompt = e;
    // Show install button
    if (installButton) {
        installButton.style.display = 'block';
    }
});

if (installButton) {
    installButton.addEventListener('click', (e) => {
        // Hide the install button
        installButton.style.display = 'none';
        // Show the install prompt
        deferredPrompt.prompt();
        // Wait for the user to respond to the prompt
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
            deferredPrompt = null;
        });
    });
}

// Mobile-specific features
class MobileFeatures {
    constructor() {
        this.init();
    }

    init() {
        this.setupTouchGestures();
        this.setupOfflineDetection();
        this.setupMobileNavigation();
        this.setupPullToRefresh();
    }

    setupTouchGestures() {
        // Add touch-friendly interactions
        document.addEventListener('touchstart', this.handleTouchStart, false);
        document.addEventListener('touchmove', this.handleTouchMove, false);
        
        // Add haptic feedback for buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('touchstart', () => {
                if (navigator.vibrate) {
                    navigator.vibrate(10); // Short vibration
                }
            });
        });
    }

    handleTouchStart(evt) {
        const firstTouch = evt.touches[0];
        this.xDown = firstTouch.clientX;
        this.yDown = firstTouch.clientY;
    }

    handleTouchMove(evt) {
        if (!this.xDown || !this.yDown) {
            return;
        }

        const xUp = evt.touches[0].clientX;
        const yUp = evt.touches[0].clientY;

        const xDiff = this.xDown - xUp;
        const yDiff = this.yDown - yUp;

        // Swipe gestures can be implemented here
        if (Math.abs(xDiff) > Math.abs(yDiff)) {
            if (xDiff > 0) {
                // Left swipe
                this.handleLeftSwipe();
            } else {
                // Right swipe
                this.handleRightSwipe();
            }
        }

        this.xDown = null;
        this.yDown = null;
    }

    handleLeftSwipe() {
        // Implement left swipe functionality
        console.log('Left swipe detected');
    }

    handleRightSwipe() {
        // Implement right swipe functionality
        console.log('Right swipe detected');
    }

    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.showConnectionStatus('متصل', 'success');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.showConnectionStatus('غير متصل', 'warning');
        });

        // Check initial connection status
        if (!navigator.onLine) {
            this.showConnectionStatus('غير متصل', 'warning');
        }
    }

    showConnectionStatus(message, type) {
        // Remove existing status
        const existingStatus = document.querySelector('.connection-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        // Create status element
        const statusElement = document.createElement('div');
        statusElement.className = `alert alert-${type} connection-status position-fixed`;
        statusElement.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 200px;
            animation: slideIn 0.3s ease-out;
        `;
        statusElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'wifi' : 'exclamation-triangle'}"></i>
            ${message}
        `;

        document.body.appendChild(statusElement);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (statusElement.parentNode) {
                statusElement.remove();
            }
        }, 3000);
    }

    syncOfflineData() {
        // Sync any offline data when connection is restored
        const offlineData = localStorage.getItem('offlineData');
        if (offlineData) {
            try {
                const data = JSON.parse(offlineData);
                // Process offline data
                console.log('Syncing offline data:', data);
                // Clear offline data after sync
                localStorage.removeItem('offlineData');
            } catch (e) {
                console.error('Error syncing offline data:', e);
            }
        }
    }

    setupMobileNavigation() {
        // Mobile-friendly navigation
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            let lastScrollTop = 0;
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down - hide navbar
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up - show navbar
                    navbar.style.transform = 'translateY(0)';
                }
                
                lastScrollTop = scrollTop;
            });
        }
    }

    setupPullToRefresh() {
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const threshold = 100;
        let isPulling = false;

        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].pageY;
                isPulling = true;
            }
        });

        document.addEventListener('touchmove', (e) => {
            if (!isPulling) return;

            currentY = e.touches[0].pageY;
            pullDistance = currentY - startY;

            if (pullDistance > 0 && window.scrollY === 0) {
                e.preventDefault();
                
                // Visual feedback
                const pullIndicator = this.getPullIndicator();
                if (pullDistance > threshold) {
                    pullIndicator.textContent = 'اتركه للتحديث';
                    pullIndicator.className = 'pull-indicator active';
                } else {
                    pullIndicator.textContent = 'اسحب للتحديث';
                    pullIndicator.className = 'pull-indicator';
                }
                
                pullIndicator.style.transform = `translateY(${Math.min(pullDistance / 2, 50)}px)`;
            }
        });

        document.addEventListener('touchend', () => {
            if (!isPulling) return;

            isPulling = false;
            const pullIndicator = this.getPullIndicator();

            if (pullDistance > threshold) {
                // Trigger refresh
                pullIndicator.textContent = 'جاري التحديث...';
                pullIndicator.className = 'pull-indicator refreshing';
                
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Reset
                pullIndicator.style.transform = 'translateY(-100%)';
                setTimeout(() => {
                    pullIndicator.remove();
                }, 300);
            }

            pullDistance = 0;
        });
    }

    getPullIndicator() {
        let indicator = document.querySelector('.pull-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'pull-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 50%;
                transform: translateX(-50%) translateY(-100%);
                background: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 0 0 10px 10px;
                z-index: 9999;
                transition: all 0.3s ease;
                font-size: 14px;
            `;
            document.body.appendChild(indicator);
        }
        return indicator;
    }
}

// Initialize mobile features
document.addEventListener('DOMContentLoaded', () => {
    new MobileFeatures();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .pull-indicator.active {
        background: #28a745 !important;
    }

    .pull-indicator.refreshing {
        background: #ffc107 !important;
    }

    .navbar {
        transition: transform 0.3s ease;
    }

    /* Mobile-specific styles */
    @media (max-width: 768px) {
        .btn {
            min-height: 44px; /* Touch-friendly button size */
        }
        
        .table-responsive {
            font-size: 14px;
        }
        
        .card {
            margin-bottom: 1rem;
        }
        
        .form-control {
            min-height: 44px;
        }
    }
`;
document.head.appendChild(style);
