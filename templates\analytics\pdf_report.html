<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير شامل - {{ company_name }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .report-date {
            color: #666;
            font-size: 14px;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        
        .table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .currency {
            font-weight: bold;
            color: #28a745;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ company_name }}</div>
        <div class="report-title">التقرير الشامل</div>
        <div class="report-date">تاريخ التقرير: {{ report_date|date:"d/m/Y" }}</div>
    </div>

    <!-- إحصائيات المبيعات -->
    <div class="section">
        <h2 class="section-title">إحصائيات المبيعات</h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ sales_stats.total_revenue|floatformat:2 }} د.أ</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ sales_stats.total_orders }}</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
        </div>
        
        <p>يُظهر هذا القسم الأداء المالي للمبيعات خلال الفترة المحددة، مما يساعد في تقييم نمو الأعمال واتخاذ القرارات الاستراتيجية.</p>
    </div>

    <!-- إحصائيات الإنتاج -->
    <div class="section">
        <h2 class="section-title">إحصائيات الإنتاج</h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ production_stats.total_production|floatformat:0 }} كغم</div>
                <div class="stat-label">إجمالي الإنتاج</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ production_stats.total_batches }}</div>
                <div class="stat-label">عدد دفعات الإنتاج</div>
            </div>
        </div>
        
        <p>تُوضح هذه الإحصائيات كفاءة العمليات الإنتاجية والقدرة الإنتاجية للمصنع، مما يساعد في تخطيط الإنتاج المستقبلي.</p>
    </div>

    <!-- تحليل الأداء -->
    <div class="section">
        <h2 class="section-title">تحليل الأداء</h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ sales_stats.avg_order_value|floatformat:2 }} د.أ</div>
                <div class="stat-label">متوسط قيمة الطلب</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ production_stats.avg_batch_size|floatformat:0 }} كغم</div>
                <div class="stat-label">متوسط حجم الدفعة</div>
            </div>
        </div>
        
        <p>تُساعد مؤشرات الأداء هذه في فهم كفاءة العمليات وتحديد المجالات التي تحتاج إلى تحسين.</p>
    </div>

    <!-- ملخص تنفيذي -->
    <div class="section">
        <h2 class="section-title">الملخص التنفيذي</h2>
        
        <p><strong>نظرة عامة على الأداء:</strong></p>
        <ul>
            <li>تم تحقيق إيرادات إجمالية قدرها <span class="currency">{{ sales_stats.total_revenue|floatformat:2 }} د.أ</span> من خلال {{ sales_stats.total_orders }} طلب</li>
            <li>تم إنتاج <span class="currency">{{ production_stats.total_production|floatformat:0 }} كغم</span> من الأعلاف في {{ production_stats.total_batches }} دفعة إنتاج</li>
            <li>متوسط قيمة الطلب الواحد:
                <span class="currency">{{ sales_stats.avg_order_value|floatformat:2 }} د.أ</span>
            </li>
        </ul>
        
        <p><strong>التوصيات:</strong></p>
        <ul>
            <li>مراجعة استراتيجيات التسعير لتحسين الهوامش الربحية</li>
            <li>تحسين كفاءة العمليات الإنتاجية لزيادة الإنتاجية</li>
            <li>تطوير خطط تسويقية لزيادة عدد الطلبات</li>
            <li>مراقبة مستويات المخزون لضمان استمرارية الإنتاج</li>
        </ul>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة {{ company_name }}</p>
        <p>تاريخ الإنشاء: {% now "d/m/Y H:i" %}</p>
    </div>
</body>
</html>
