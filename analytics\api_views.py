from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.db.models import Sum, Count, Avg, F
from .models import ProductionBatch, SalesReport
from orders.models import Order
from ingredients.models import Ingredient
from mixtures.models import FeedMixture


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def production_list_create(request):
    """قائمة وإنشاء دفعات الإنتاج"""
    if request.method == 'GET':
        batches = ProductionBatch.objects.select_related('mixture', 'produced_by').order_by('-production_date')[:20]
        data = []
        for batch in batches:
            data.append({
                'id': batch.id,
                'batch_number': batch.batch_number,
                'mixture': {
                    'id': batch.mixture.id,
                    'name': batch.mixture.name
                },
                'quantity_produced_kg': float(batch.quantity_produced_kg),
                'production_date': batch.production_date,
                'produced_by': batch.produced_by.username,
                'material_cost': float(batch.material_cost),
                'labor_cost': float(batch.labor_cost),
                'overhead_cost': float(batch.overhead_cost),
                'total_cost': float(batch.total_cost),
                'cost_per_kg': float(batch.cost_per_kg)
            })
        return Response(data)
    
    elif request.method == 'POST':
        # إنشاء دفعة إنتاج جديدة - سيتم تطويره لاحقاً
        return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def production_detail(request, pk):
    """تفاصيل دفعة الإنتاج"""
    try:
        batch = ProductionBatch.objects.select_related('mixture', 'produced_by').get(pk=pk)
    except ProductionBatch.DoesNotExist:
        return Response({'error': 'دفعة الإنتاج غير موجودة'}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        return Response({
            'id': batch.id,
            'batch_number': batch.batch_number,
            'mixture': {
                'id': batch.mixture.id,
                'name': batch.mixture.name,
                'feed_type': batch.mixture.feed_type.name
            },
            'quantity_produced_kg': float(batch.quantity_produced_kg),
            'production_date': batch.production_date,
            'produced_by': {
                'id': batch.produced_by.id,
                'username': batch.produced_by.username
            },
            'material_cost': float(batch.material_cost),
            'labor_cost': float(batch.labor_cost),
            'overhead_cost': float(batch.overhead_cost),
            'total_cost': float(batch.total_cost),
            'cost_per_kg': float(batch.cost_per_kg),
            'notes': batch.notes,
            'created_at': batch.created_at,
            'updated_at': batch.updated_at
        })
    
    # تحديث وحذف دفعة الإنتاج - سيتم تطويرهما لاحقاً
    return Response({'message': 'قيد التطوير'}, status=status.HTTP_501_NOT_IMPLEMENTED)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_stats(request):
    """إحصائيات شاملة للنظام"""
    
    # إحصائيات الإنتاج
    total_batches = ProductionBatch.objects.count()
    total_production = ProductionBatch.objects.aggregate(
        total=Sum('quantity_produced_kg')
    )['total'] or 0
    total_production_cost = ProductionBatch.objects.aggregate(
        total=Sum('total_cost')
    )['total'] or 0
    avg_production_cost = ProductionBatch.objects.aggregate(
        avg=Avg('cost_per_kg')
    )['avg'] or 0
    
    # إحصائيات المبيعات
    total_orders = Order.objects.count()
    total_revenue = Order.objects.aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    total_paid = Order.objects.aggregate(
        total=Sum('paid_amount')
    )['total'] or 0
    
    # إحصائيات المكونات
    total_ingredients = Ingredient.objects.filter(is_active=True).count()
    low_stock_ingredients = Ingredient.objects.filter(
        current_stock__lte=F('minimum_stock'),
        is_active=True
    ).count()
    
    # إحصائيات الخلطات
    total_mixtures = FeedMixture.objects.filter(is_active=True).count()
    complete_formulas = sum(1 for mixture in FeedMixture.objects.filter(is_active=True) if mixture.is_formula_complete)
    
    # حساب الربح الإجمالي
    total_profit = float(total_revenue) - float(total_production_cost)
    profit_margin = (total_profit / float(total_revenue) * 100) if total_revenue > 0 else 0
    
    return Response({
        'production_stats': {
            'total_batches': total_batches,
            'total_production_kg': round(float(total_production), 2),
            'total_production_cost': round(float(total_production_cost), 3),
            'average_cost_per_kg': round(float(avg_production_cost), 3)
        },
        'sales_stats': {
            'total_orders': total_orders,
            'total_revenue': round(float(total_revenue), 3),
            'total_paid': round(float(total_paid), 3),
            'outstanding_amount': round(float(total_revenue - total_paid), 3)
        },
        'inventory_stats': {
            'total_ingredients': total_ingredients,
            'low_stock_ingredients': low_stock_ingredients,
            'total_mixtures': total_mixtures,
            'complete_formulas': complete_formulas,
            'incomplete_formulas': total_mixtures - complete_formulas
        },
        'financial_summary': {
            'total_revenue': round(float(total_revenue), 3),
            'total_costs': round(float(total_production_cost), 3),
            'total_profit': round(total_profit, 3),
            'profit_margin_percentage': round(profit_margin, 2)
        }
    })
