"""
Widgets للوحة التحكم المتقدمة
"""

from django.db.models import Sum, Count, Avg, F
from django.utils import timezone
from datetime import datetime, timedelta
from orders.models import Order, Customer
from ingredients.models import Ingredient
from analytics.models import ProductionBatch
from invoices.models import Invoice
import json


class DashboardWidget:
    """فئة أساسية للـ widgets"""
    
    def __init__(self, title, widget_type, size='col-md-6'):
        self.title = title
        self.widget_type = widget_type
        self.size = size
        self.data = {}
    
    def get_data(self):
        """جلب البيانات للـ widget"""
        return self.data
    
    def render_context(self):
        """إعداد السياق للعرض"""
        return {
            'title': self.title,
            'type': self.widget_type,
            'size': self.size,
            'data': self.get_data()
        }


class SalesStatsWidget(DashboardWidget):
    """widget إحصائيات المبيعات"""
    
    def __init__(self):
        super().__init__('إحصائيات المبيعات', 'stats_card', 'col-lg-3')
    
    def get_data(self):
        today = timezone.now().date()
        this_month = today.replace(day=1)
        last_month = (this_month - timedelta(days=1)).replace(day=1)
        
        # إحصائيات هذا الشهر
        current_month_orders = Order.objects.filter(order_date__gte=this_month)
        current_revenue = current_month_orders.aggregate(total=Sum('total_amount'))['total'] or 0
        current_orders_count = current_month_orders.count()
        
        # إحصائيات الشهر الماضي
        last_month_orders = Order.objects.filter(
            order_date__gte=last_month,
            order_date__lt=this_month
        )
        last_revenue = last_month_orders.aggregate(total=Sum('total_amount'))['total'] or 0
        last_orders_count = last_month_orders.count()
        
        # حساب النمو
        revenue_growth = 0
        orders_growth = 0
        
        if last_revenue > 0:
            revenue_growth = ((current_revenue - last_revenue) / last_revenue) * 100
        
        if last_orders_count > 0:
            orders_growth = ((current_orders_count - last_orders_count) / last_orders_count) * 100
        
        return {
            'current_revenue': current_revenue,
            'current_orders': current_orders_count,
            'revenue_growth': revenue_growth,
            'orders_growth': orders_growth,
            'avg_order_value': current_revenue / current_orders_count if current_orders_count > 0 else 0
        }


class ProductionStatsWidget(DashboardWidget):
    """widget إحصائيات الإنتاج"""
    
    def __init__(self):
        super().__init__('إحصائيات الإنتاج', 'production_stats', 'col-lg-3')
    
    def get_data(self):
        today = timezone.now().date()
        this_month = today.replace(day=1)
        
        # إنتاج هذا الشهر
        current_production = ProductionBatch.objects.filter(
            production_date__gte=this_month
        ).aggregate(
            total_kg=Sum('quantity_produced_kg'),
            total_batches=Count('id'),
            avg_batch_size=Avg('quantity_produced_kg'),
            total_cost=Sum('total_cost')
        )
        
        # أكثر المنتجات إنتاجاً
        top_products = ProductionBatch.objects.filter(
            production_date__gte=this_month
        ).values('mixture__name').annotate(
            total_produced=Sum('quantity_produced_kg')
        ).order_by('-total_produced')[:3]
        
        return {
            'total_production': current_production['total_kg'] or 0,
            'total_batches': current_production['total_batches'] or 0,
            'avg_batch_size': current_production['avg_batch_size'] or 0,
            'total_cost': current_production['total_cost'] or 0,
            'top_products': list(top_products)
        }


class InventoryAlertsWidget(DashboardWidget):
    """widget تنبيهات المخزون"""
    
    def __init__(self):
        super().__init__('تنبيهات المخزون', 'inventory_alerts', 'col-lg-6')
    
    def get_data(self):
        # المكونات منخفضة المخزون
        low_stock = Ingredient.objects.filter(
            current_stock__lt=F('minimum_stock'),
            is_active=True
        ).order_by('current_stock')[:10]
        
        # المكونات المضافة حديثاً (بدلاً من انتهاء الصلاحية)
        recent_ingredients = Ingredient.objects.filter(
            is_active=True
        ).order_by('-created_at')[:5]
        
        # إجمالي قيمة المخزون
        total_inventory_value = Ingredient.objects.filter(
            is_active=True
        ).aggregate(
            total_value=Sum(F('current_stock') * F('cost_per_kg'))
        )['total_value'] or 0
        
        return {
            'low_stock_items': [
                {
                    'name': item.name,
                    'current_stock': item.current_stock,
                    'minimum_level': item.minimum_stock,
                    'percentage': (item.current_stock / item.minimum_stock * 100) if item.minimum_stock > 0 else 0
                }
                for item in low_stock
            ],
            'recent_items': [
                {
                    'name': item.name,
                    'created_date': item.created_at.date(),
                    'current_stock': item.current_stock,
                    'cost_per_kg': item.cost_per_kg
                }
                for item in recent_ingredients
            ],
            'total_inventory_value': total_inventory_value,
            'low_stock_count': low_stock.count(),
            'recent_items_count': recent_ingredients.count()
        }


class RecentOrdersWidget(DashboardWidget):
    """widget الطلبات الأخيرة"""
    
    def __init__(self):
        super().__init__('الطلبات الأخيرة', 'recent_orders', 'col-lg-6')
    
    def get_data(self):
        recent_orders = Order.objects.select_related('customer').order_by('-created_at')[:5]
        
        return {
            'orders': [
                {
                    'id': order.pk,
                    'order_number': order.order_number,
                    'customer_name': order.customer.name,
                    'total_amount': order.total_amount,
                    'status': order.status,
                    'status_display': order.get_status_display(),
                    'created_at': order.created_at,
                    'time_ago': self._time_ago(order.created_at)
                }
                for order in recent_orders
            ]
        }
    
    def _time_ago(self, datetime_obj):
        """حساب الوقت المنقضي"""
        now = timezone.now()
        diff = now - datetime_obj
        
        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"


class SalesChartWidget(DashboardWidget):
    """widget رسم بياني للمبيعات"""
    
    def __init__(self):
        super().__init__('تطور المبيعات', 'sales_chart', 'col-lg-8')
    
    def get_data(self):
        # بيانات آخر 30 يوم
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=29)
        
        daily_sales = []
        labels = []
        
        current_date = start_date
        while current_date <= end_date:
            daily_revenue = Order.objects.filter(
                order_date=current_date
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            daily_sales.append(float(daily_revenue))
            labels.append(current_date.strftime('%d/%m'))
            
            current_date += timedelta(days=1)
        
        return {
            'labels': labels,
            'data': daily_sales,
            'total_period_sales': sum(daily_sales),
            'avg_daily_sales': sum(daily_sales) / len(daily_sales) if daily_sales else 0
        }


class TopCustomersWidget(DashboardWidget):
    """widget أفضل العملاء"""
    
    def __init__(self):
        super().__init__('أفضل العملاء', 'top_customers', 'col-lg-4')
    
    def get_data(self):
        # أفضل العملاء حسب إجمالي المشتريات
        top_customers = Customer.objects.annotate(
            total_orders=Count('orders'),
            total_spent=Sum('orders__total_amount')
        ).filter(
            total_spent__gt=0
        ).order_by('-total_spent')[:5]
        
        return {
            'customers': [
                {
                    'id': customer.pk,
                    'name': customer.name,
                    'total_orders': customer.total_orders,
                    'total_spent': customer.total_spent,
                    'avg_order_value': customer.total_spent / customer.total_orders if customer.total_orders > 0 else 0
                }
                for customer in top_customers
            ]
        }


class FinancialSummaryWidget(DashboardWidget):
    """widget الملخص المالي"""
    
    def __init__(self):
        super().__init__('الملخص المالي', 'financial_summary', 'col-lg-12')
    
    def get_data(self):
        today = timezone.now().date()
        this_month = today.replace(day=1)
        
        # الإيرادات
        total_revenue = Order.objects.aggregate(total=Sum('total_amount'))['total'] or 0
        monthly_revenue = Order.objects.filter(
            order_date__gte=this_month
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        # الفواتير
        total_invoiced = Invoice.objects.aggregate(total=Sum('total_amount'))['total'] or 0
        total_paid = Invoice.objects.aggregate(total=Sum('paid_amount'))['total'] or 0
        outstanding_amount = total_invoiced - total_paid
        
        # تكاليف الإنتاج
        production_costs = ProductionBatch.objects.filter(
            production_date__gte=this_month
        ).aggregate(total=Sum('total_cost'))['total'] or 0
        
        # قيمة المخزون
        inventory_value = Ingredient.objects.filter(
            is_active=True
        ).aggregate(
            total_value=Sum(F('current_stock') * F('cost_per_kg'))
        )['total_value'] or 0
        
        return {
            'total_revenue': total_revenue,
            'monthly_revenue': monthly_revenue,
            'total_invoiced': total_invoiced,
            'total_paid': total_paid,
            'outstanding_amount': outstanding_amount,
            'production_costs': production_costs,
            'inventory_value': inventory_value,
            'gross_profit': monthly_revenue - production_costs
        }


class DashboardManager:
    """مدير widgets لوحة التحكم"""
    
    def __init__(self):
        self.widgets = [
            SalesStatsWidget(),
            ProductionStatsWidget(),
            InventoryAlertsWidget(),
            RecentOrdersWidget(),
            SalesChartWidget(),
            TopCustomersWidget(),
            FinancialSummaryWidget(),
        ]
    
    def get_all_widgets(self):
        """الحصول على جميع الـ widgets مع بياناتها"""
        return [widget.render_context() for widget in self.widgets]
    
    def get_widget_by_type(self, widget_type):
        """الحصول على widget محدد"""
        for widget in self.widgets:
            if widget.widget_type == widget_type:
                return widget.render_context()
        return None
