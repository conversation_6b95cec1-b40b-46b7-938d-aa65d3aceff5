{% extends 'base.html' %}

{% block title %}لوحة التحكم المتقدمة - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active">لوحة التحكم المتقدمة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-tachometer-alt text-primary"></i>
                لوحة التحكم المتقدمة
            </h1>
            <div>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
                <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#customizeModal">
                    <i class="fas fa-cog"></i>
                    تخصيص
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card bg-primary">
            <div class="stats-number">{{ stats.total_ingredients }}</div>
            <div class="stats-label">
                <i class="fas fa-seedling"></i>
                المكونات النشطة
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card bg-success">
            <div class="stats-number">{{ stats.total_mixtures }}</div>
            <div class="stats-label">
                <i class="fas fa-blender"></i>
                خلطات الأعلاف
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ stats.total_customers }}</div>
            <div class="stats-label">
                <i class="fas fa-users"></i>
                العملاء النشطون
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card bg-warning">
            <div class="stats-number">{{ stats.pending_orders }}</div>
            <div class="stats-label">
                <i class="fas fa-clock"></i>
                طلبات في الانتظار
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-8 col-sm-12 mb-3">
        <div class="stats-card bg-gradient-primary">
            <div class="stats-number">{{ stats.total_revenue|floatformat:0 }} د.أ</div>
            <div class="stats-label">
                <i class="fas fa-dollar-sign"></i>
                إجمالي الإيرادات
            </div>
        </div>
    </div>
</div>

<!-- Widgets المتقدمة -->
<div class="row" id="dashboard-widgets">
    {% for widget in widgets %}
        <div class="{{ widget.size }} mb-4" data-widget-type="{{ widget.type }}">
            {% if widget.type == 'stats_card' %}
                <!-- Widget إحصائيات المبيعات -->
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-gradient-primary text-white">
                        <h6 class="mb-0">{{ widget.title }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ widget.data.current_revenue|floatformat:0 }}</h4>
                                    <small class="text-muted">إيرادات هذا الشهر (د.أ)</small>
                                    <div class="mt-2">
                                        <span class="badge {% if widget.data.revenue_growth >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if widget.data.revenue_growth >= 0 %}+{% endif %}{{ widget.data.revenue_growth|floatformat:1 }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-success">{{ widget.data.current_orders }}</h4>
                                    <small class="text-muted">عدد الطلبات</small>
                                    <div class="mt-2">
                                        <span class="badge {% if widget.data.orders_growth >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if widget.data.orders_growth >= 0 %}+{% endif %}{{ widget.data.orders_growth|floatformat:1 }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">متوسط قيمة الطلب: {{ widget.data.avg_order_value|floatformat:2 }} د.أ</small>
                        </div>
                    </div>
                </div>

            {% elif widget.type == 'sales_chart' %}
                <!-- Widget الرسم البياني -->
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-gradient-info text-white">
                        <h6 class="mb-0">{{ widget.title }}</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" height="100"></canvas>
                        <div class="row mt-3">
                            <div class="col-6 text-center">
                                <small class="text-muted">إجمالي الفترة</small>
                                <h6 class="text-primary">{{ widget.data.total_period_sales|floatformat:0 }} د.أ</h6>
                            </div>
                            <div class="col-6 text-center">
                                <small class="text-muted">متوسط يومي</small>
                                <h6 class="text-success">{{ widget.data.avg_daily_sales|floatformat:0 }} د.أ</h6>
                            </div>
                        </div>
                    </div>
                </div>

            {% elif widget.type == 'inventory_alerts' %}
                <!-- Widget تنبيهات المخزون -->
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-gradient-warning text-white">
                        <h6 class="mb-0">{{ widget.title }}</h6>
                    </div>
                    <div class="card-body">
                        {% if widget.data.low_stock_items %}
                            <div class="mb-3">
                                <h6 class="text-danger">مخزون منخفض ({{ widget.data.low_stock_count }})</h6>
                                {% for item in widget.data.low_stock_items|slice:":3" %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ item.name }}</span>
                                        <div>
                                            <span class="badge bg-danger">{{ item.current_stock|floatformat:0 }} كغم</span>
                                            <div class="progress mt-1" style="height: 4px; width: 60px;">
                                                <div class="progress-bar bg-danger" style="width: {{ item.percentage }}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {% if widget.data.recent_items %}
                            <div class="mb-3">
                                <h6 class="text-info">مكونات مضافة حديثاً ({{ widget.data.recent_items_count }})</h6>
                                {% for item in widget.data.recent_items|slice:":3" %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ item.name }}</span>
                                        <div>
                                            <span class="badge bg-info">{{ item.current_stock|floatformat:0 }} كغم</span>
                                            <small class="text-muted ms-1">{{ item.cost_per_kg }} د.أ/كغم</small>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">قيمة المخزون الإجمالية</small>
                            <h6 class="text-primary">{{ widget.data.total_inventory_value|floatformat:0 }} د.أ</h6>
                        </div>
                    </div>
                </div>

            {% elif widget.type == 'recent_orders' %}
                <!-- Widget الطلبات الأخيرة -->
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-gradient-success text-white">
                        <h6 class="mb-0">{{ widget.title }}</h6>
                    </div>
                    <div class="card-body">
                        {% if widget.data.orders %}
                            {% for order in widget.data.orders %}
                                <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                                    <div>
                                        <strong>{{ order.order_number }}</strong><br>
                                        <small class="text-muted">{{ order.customer_name }}</small><br>
                                        <small class="text-muted">{{ order.time_ago }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {% if order.status == 'completed' %}bg-success{% elif order.status == 'pending' %}bg-warning{% else %}bg-secondary{% endif %}">
                                            {{ order.status_display }}
                                        </span><br>
                                        <strong class="text-primary">{{ order.total_amount|floatformat:2 }} د.أ</strong>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>لا توجد طلبات حديثة</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

            {% elif widget.type == 'top_customers' %}
                <!-- Widget أفضل العملاء -->
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-gradient-secondary text-white">
                        <h6 class="mb-0">{{ widget.title }}</h6>
                    </div>
                    <div class="card-body">
                        {% if widget.data.customers %}
                            {% for customer in widget.data.customers %}
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <strong>{{ customer.name }}</strong><br>
                                        <small class="text-muted">{{ customer.total_orders }} طلب</small>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success">{{ customer.total_spent|floatformat:0 }} د.أ</strong><br>
                                        <small class="text-muted">متوسط: {{ customer.avg_order_value|floatformat:0 }} د.أ</small>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <p>لا توجد بيانات عملاء</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

            {% endif %}
        </div>
    {% endfor %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات
{% for widget in widgets %}
    {% if widget.type == 'sales_chart' %}
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: {{ widget.data.labels|safe }},
                datasets: [{
                    label: 'المبيعات اليومية (د.أ)',
                    data: {{ widget.data.data|safe }},
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' د.أ';
                            }
                        }
                    }
                }
            }
        });
    {% endif %}
{% endfor %}

// تحديث لوحة التحكم
function refreshDashboard() {
    location.reload();
}

// تحديث تلقائي كل 5 دقائق
setInterval(refreshDashboard, 300000);
</script>
{% endblock %}
