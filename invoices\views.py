from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Q
from django.template.loader import get_template
from django.conf import settings
from .models import Invoice, InvoiceItem, Payment
from orders.models import Order
from datetime import datetime, timedelta
import json

# PDF generation imports
WEASYPRINT_AVAILABLE = False
try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError):
    # WeasyPrint not available or missing dependencies
    pass


@login_required
def invoice_list(request):
    """قائمة الفواتير"""
    invoices = Invoice.objects.select_related('customer', 'order').all()

    # تصفية حسب الحالة
    status = request.GET.get('status')
    if status:
        invoices = invoices.filter(status=status)

    # تصفية حسب العميل
    customer_id = request.GET.get('customer')
    if customer_id:
        invoices = invoices.filter(customer_id=customer_id)

    # البحث
    search = request.GET.get('search')
    if search:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    # إحصائيات
    stats = {
        'total_invoices': invoices.count(),
        'total_amount': invoices.aggregate(total=Sum('total_amount'))['total'] or 0,
        'paid_amount': invoices.aggregate(total=Sum('paid_amount'))['total'] or 0,
        'pending_amount': 0,
    }
    stats['pending_amount'] = stats['total_amount'] - stats['paid_amount']

    context = {
        'invoices': invoices,
        'stats': stats,
        'status_choices': Invoice.INVOICE_STATUS_CHOICES,
    }

    return render(request, 'invoices/list.html', context)


@login_required
def invoice_detail(request, pk):
    """تفاصيل الفاتورة"""
    invoice = get_object_or_404(Invoice, pk=pk)
    payments = invoice.payments.all()

    context = {
        'invoice': invoice,
        'payments': payments,
    }

    return render(request, 'invoices/detail.html', context)


@login_required
def invoice_create_from_order(request, order_id):
    """إنشاء فاتورة من طلب"""
    order = get_object_or_404(Order, pk=order_id)

    # التحقق من عدم وجود فاتورة للطلب
    if hasattr(order, 'invoice'):
        messages.warning(request, 'يوجد فاتورة لهذا الطلب بالفعل')
        return redirect('invoices:detail', pk=order.invoice.pk)

    if request.method == 'POST':
        # إنشاء الفاتورة
        due_date = datetime.now().date() + timedelta(days=30)  # 30 يوم من اليوم

        invoice = Invoice.objects.create(
            order=order,
            customer=order.customer,
            due_date=due_date,
            created_by=request.user
        )

        # إنشاء عناصر الفاتورة من عناصر الطلب
        for item in order.items.all():
            InvoiceItem.objects.create(
                invoice=invoice,
                description=f"{item.mixture.name} - {item.mixture.feed_type.name}",
                quantity=item.quantity_kg,
                unit_price=item.unit_price
            )

        messages.success(request, f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح')
        return redirect('invoices:detail', pk=invoice.pk)

    # حساب تاريخ الاستحقاق للعرض
    due_date = datetime.now().date() + timedelta(days=30)

    context = {
        'order': order,
        'due_date': due_date,
    }

    return render(request, 'invoices/create_from_order.html', context)


@login_required
def invoice_pdf(request, pk):
    """تصدير الفاتورة كـ PDF"""
    if not WEASYPRINT_AVAILABLE:
        messages.error(request, 'مكتبة PDF غير متاحة. يرجى تثبيت WeasyPrint')
        return redirect('invoices:detail', pk=pk)

    invoice = get_object_or_404(Invoice, pk=pk)

    # إعداد السياق للقالب
    context = {
        'invoice': invoice,
        'company_name': 'مصنع الأعلاف',
        'company_address': 'عمان، الأردن',
        'company_phone': '+962 6 123 4567',
        'company_email': '<EMAIL>',
    }

    # تحميل القالب
    template = get_template('invoices/pdf_template.html')
    html_string = template.render(context)

    # إنشاء PDF
    html = HTML(string=html_string, base_url=request.build_absolute_uri())

    # إعداد CSS للخطوط العربية
    css = CSS(string='''
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        .ltr { direction: ltr; text-align: left; }
    ''')

    pdf = html.write_pdf(stylesheets=[css])

    # إرجاع PDF كاستجابة
    response = HttpResponse(pdf, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'

    return response


@login_required
def add_payment(request, invoice_id):
    """إضافة دفعة للفاتورة"""
    invoice = get_object_or_404(Invoice, pk=invoice_id)

    if request.method == 'POST':
        amount = float(request.POST.get('amount', 0))
        payment_date = request.POST.get('payment_date')
        payment_method = request.POST.get('payment_method')
        reference_number = request.POST.get('reference_number', '')
        notes = request.POST.get('notes', '')

        # التحقق من صحة المبلغ
        if amount <= 0:
            messages.error(request, 'يجب أن يكون المبلغ أكبر من صفر')
            return redirect('invoices:detail', pk=invoice_id)

        if amount > invoice.remaining_amount:
            messages.error(request, 'المبلغ أكبر من المبلغ المتبقي')
            return redirect('invoices:detail', pk=invoice_id)

        # إنشاء الدفعة
        Payment.objects.create(
            invoice=invoice,
            amount=amount,
            payment_date=payment_date,
            payment_method=payment_method,
            reference_number=reference_number,
            notes=notes,
            created_by=request.user
        )

        messages.success(request, f'تم إضافة دفعة بمبلغ {amount} د.أ بنجاح')
        return redirect('invoices:detail', pk=invoice_id)

    return redirect('invoices:detail', pk=invoice_id)


@login_required
def invoice_stats_api(request):
    """API لإحصائيات الفواتير"""
    # إحصائيات عامة
    total_invoices = Invoice.objects.count()
    total_amount = Invoice.objects.aggregate(total=Sum('total_amount'))['total'] or 0
    paid_amount = Invoice.objects.aggregate(total=Sum('paid_amount'))['total'] or 0

    # إحصائيات حسب الحالة
    status_stats = {}
    for status, label in Invoice.INVOICE_STATUS_CHOICES:
        count = Invoice.objects.filter(status=status).count()
        status_stats[status] = {'count': count, 'label': label}

    # الفواتير المتأخرة
    overdue_count = Invoice.objects.filter(
        due_date__lt=datetime.now().date(),
        status__in=['sent', 'draft']
    ).count()

    data = {
        'total_invoices': total_invoices,
        'total_amount': float(total_amount),
        'paid_amount': float(paid_amount),
        'pending_amount': float(total_amount - paid_amount),
        'status_stats': status_stats,
        'overdue_count': overdue_count,
    }

    return JsonResponse(data)
