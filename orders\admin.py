from django.contrib import admin
from .models import Customer, Order, OrderItem


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 1
    fields = ['mixture', 'quantity_kg', 'unit_price', 'total_price', 'notes']
    readonly_fields = ['total_price']


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'company_name', 'phone', 'email', 'city',
        'credit_limit', 'current_balance', 'available_credit', 'is_active'
    ]
    list_filter = ['city', 'is_active', 'created_at']
    search_fields = ['name', 'company_name', 'phone', 'email']
    list_editable = ['credit_limit', 'current_balance', 'is_active']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'company_name', 'phone', 'email', 'is_active')
        }),
        ('معلومات العنوان', {
            'fields': ('address', 'city')
        }),
        ('معلومات مالية', {
            'fields': ('credit_limit', 'current_balance')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )

    def available_credit(self, obj):
        return f"{obj.available_credit:.3f} د.ع"
    available_credit.short_description = 'الائتمان المتاح'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = [
        'order_number', 'customer', 'order_date', 'required_date',
        'status', 'payment_status', 'total_amount', 'remaining_amount'
    ]
    list_filter = ['status', 'payment_status', 'order_date', 'required_date']
    search_fields = ['order_number', 'customer__name', 'customer__company_name']
    list_editable = ['status', 'payment_status']
    inlines = [OrderItemInline]

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('order_number', 'customer', 'created_by', 'required_date')
        }),
        ('حالة الطلب', {
            'fields': ('status', 'payment_status', 'delivery_date')
        }),
        ('المبالغ المالية', {
            'fields': (
                'subtotal', 'discount_percentage', 'discount_amount',
                'tax_percentage', 'tax_amount', 'total_amount', 'paid_amount'
            )
        }),
        ('معلومات إضافية', {
            'fields': ('delivery_address', 'notes')
        }),
    )

    readonly_fields = ['order_number', 'subtotal', 'discount_amount', 'tax_amount', 'total_amount']

    def remaining_amount(self, obj):
        return f"{obj.remaining_amount:.3f} د.أ"
    remaining_amount.short_description = 'المبلغ المتبقي'

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان طلب جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
        # إعادة حساب المجاميع
        obj.calculate_totals()


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'mixture', 'quantity_kg', 'unit_price', 'total_price']
    list_filter = ['order__status', 'mixture__feed_type']
    search_fields = ['order__order_number', 'mixture__name']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('order', 'mixture')
