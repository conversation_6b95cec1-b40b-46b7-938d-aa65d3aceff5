#!/bin/bash

# انتظار قاعدة البيانات
echo "انتظار قاعدة البيانات..."
while ! nc -z $DB_HOST $DB_PORT; do
  sleep 0.1
done
echo "قاعدة البيانات متاحة!"

# تطبيق الهجرات
echo "تطبيق هجرات قاعدة البيانات..."
python manage.py migrate

# جمع الملفات الثابتة
echo "جمع الملفات الثابتة..."
python manage.py collectstatic --noinput

# إنشاء مستخدم إداري إذا لم يكن موجوداً
echo "إنشاء المستخدم الإداري..."
python manage.py shell << EOF
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('تم إنشاء المستخدم الإداري')
else:
    print('المستخدم الإداري موجود بالفعل')
EOF

# تشغيل الخادم
echo "تشغيل خادم Django..."
exec python manage.py runserver 0.0.0.0:8000
