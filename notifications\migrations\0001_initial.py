# Generated by Django 5.2.1 on 2025-06-03 21:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القالب')),
                ('title_template', models.CharField(max_length=200, verbose_name='قالب العنوان')),
                ('message_template', models.TextField(verbose_name='قالب الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('warning', 'تحذير'), ('success', 'نجاح'), ('error', 'خطأ'), ('order', 'طلب جديد'), ('payment', 'دفعة جديدة'), ('stock', 'تنبيه مخزون'), ('invoice', 'فاتورة جديدة')], default='info', max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('send_email', models.BooleanField(default=False, verbose_name='إرسال بريد إلكتروني')),
                ('send_sms', models.BooleanField(default=False, verbose_name='إرسال رسالة نصية')),
                ('send_push', models.BooleanField(default=True, verbose_name='إرسال إشعار فوري')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب إشعار',
                'verbose_name_plural': 'قوالب الإشعارات',
            },
        ),
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')),
                ('sms_notifications', models.BooleanField(default=False, verbose_name='الرسائل النصية')),
                ('push_notifications', models.BooleanField(default=True, verbose_name='الإشعارات الفورية')),
                ('order_notifications', models.BooleanField(default=True, verbose_name='إشعارات الطلبات')),
                ('payment_notifications', models.BooleanField(default=True, verbose_name='إشعارات الدفعات')),
                ('stock_notifications', models.BooleanField(default=True, verbose_name='تنبيهات المخزون')),
                ('invoice_notifications', models.BooleanField(default=True, verbose_name='إشعارات الفواتير')),
                ('quiet_hours_start', models.TimeField(default='22:00', verbose_name='بداية الساعات الهادئة')),
                ('quiet_hours_end', models.TimeField(default='08:00', verbose_name='نهاية الساعات الهادئة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إعدادات الإشعارات',
                'verbose_name_plural': 'إعدادات الإشعارات',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('warning', 'تحذير'), ('success', 'نجاح'), ('error', 'خطأ'), ('order', 'طلب جديد'), ('payment', 'دفعة جديدة'), ('stock', 'تنبيه مخزون'), ('invoice', 'فاتورة جديدة')], default='info', max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('action_text', models.CharField(blank=True, max_length=100, null=True, verbose_name='نص الإجراء')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('data', models.JSONField(blank=True, default=dict, verbose_name='بيانات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='notificatio_user_id_427e4b_idx'), models.Index(fields=['created_at'], name='notificatio_created_46ad24_idx'), models.Index(fields=['notification_type'], name='notificatio_notific_f2898f_idx')],
            },
        ),
    ]
