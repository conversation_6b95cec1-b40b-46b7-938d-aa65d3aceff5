from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import FeedMixture, FeedType, MixtureComponent
from .forms import FeedMixtureForm, FeedTypeForm, MixtureComponentForm, MixtureSearchForm
from ingredients.models import Ingredient


@login_required
def mixture_list(request):
    """قائمة خلطات الأعلاف"""
    try:
        form = MixtureSearchForm(request.GET)
        mixtures = FeedMixture.objects.select_related('feed_type').filter(is_active=True)
    except Exception as e:
        # في حالة وجود خطأ، أرسل رسالة خطأ
        from django.contrib import messages
        messages.error(request, f'خطأ في تحميل البيانات: {str(e)}')
        form = None
        mixtures = FeedMixture.objects.none()

    # تطبيق الفلاتر
    if form.is_valid():
        search = form.cleaned_data.get('search')
        feed_type = form.cleaned_data.get('feed_type')
        min_protein = form.cleaned_data.get('min_protein')
        max_cost = form.cleaned_data.get('max_cost')

        if search:
            mixtures = mixtures.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if feed_type:
            mixtures = mixtures.filter(feed_type=feed_type)

        if min_protein is not None:
            mixtures = mixtures.filter(target_protein_percentage__gte=min_protein)

        if max_cost is not None:
            mixtures = mixtures.filter(cost_per_kg__lte=max_cost)

    # التصفح
    paginator = Paginator(mixtures, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'mixtures': page_obj,
        'form': form,
        'page_obj': page_obj
    }
    return render(request, 'mixtures/list.html', context)


@login_required
def mixture_detail(request, pk):
    """تفاصيل الخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=pk)
    components = mixture.components.select_related('ingredient').all()
    nutritional_values = mixture.calculate_nutritional_values()

    context = {
        'mixture': mixture,
        'components': components,
        'nutritional_values': nutritional_values
    }
    return render(request, 'mixtures/detail.html', context)


@login_required
def mixture_add(request):
    """إضافة خلطة جديدة"""
    if request.method == 'POST':
        form = FeedMixtureForm(request.POST)
        if form.is_valid():
            try:
                mixture = form.save()
                messages.success(request, f'تم إضافة الخلطة "{mixture.name}" بنجاح')
                return redirect('mixtures:detail', pk=mixture.pk)
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الخلطة: {str(e)}')
        else:
            # إضافة رسائل خطأ مفصلة
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'خطأ في حقل {field}: {error}')
    else:
        form = FeedMixtureForm()

    context = {
        'form': form,
        'title': 'إضافة خلطة جديدة'
    }
    return render(request, 'mixtures/form.html', context)


@login_required
def mixture_edit(request, pk):
    """تعديل الخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=pk)

    if request.method == 'POST':
        form = FeedMixtureForm(request.POST, instance=mixture)
        if form.is_valid():
            mixture = form.save()
            messages.success(request, f'تم تحديث الخلطة "{mixture.name}" بنجاح')
            return redirect('mixtures:detail', pk=pk)
    else:
        form = FeedMixtureForm(instance=mixture)

    context = {
        'form': form,
        'mixture': mixture,
        'title': f'تعديل الخلطة: {mixture.name}'
    }
    return render(request, 'mixtures/form.html', context)


@login_required
def mixture_delete(request, pk):
    """حذف الخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=pk)
    if request.method == 'POST':
        mixture.is_active = False
        mixture.save()
        messages.success(request, 'تم حذف الخلطة بنجاح')
        return redirect('mixtures:list')

    return render(request, 'mixtures/delete.html', {'mixture': mixture})


@login_required
def mixture_calculate(request, pk):
    """حساب القيم الغذائية والتكلفة للخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=pk)

    if request.method == 'POST':
        cost = mixture.calculate_cost()
        nutritional_values = mixture.calculate_nutritional_values()

        return JsonResponse({
            'success': True,
            'cost_per_kg': float(cost),
            'nutritional_values': nutritional_values,
            'profit_margin': float(mixture.profit_margin_percentage)
        })

    return JsonResponse({'success': False})


@login_required
def feed_type_list(request):
    """قائمة أنواع الأعلاف"""
    feed_types = FeedType.objects.all()
    return render(request, 'mixtures/types.html', {'feed_types': feed_types})


@login_required
def feed_type_add(request):
    """إضافة نوع علف جديد"""
    if request.method == 'POST':
        form = FeedTypeForm(request.POST)
        if form.is_valid():
            feed_type = form.save()
            messages.success(request, f'تم إضافة نوع العلف "{feed_type.name}" بنجاح')
            return redirect('mixtures:types')
    else:
        form = FeedTypeForm()

    context = {
        'form': form,
        'title': 'إضافة نوع علف جديد'
    }
    return render(request, 'mixtures/type_form.html', context)


@login_required
def mixture_component_add(request, mixture_pk):
    """إضافة مكون للخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=mixture_pk)

    if request.method == 'POST':
        form = MixtureComponentForm(request.POST, mixture=mixture)
        if form.is_valid():
            component = form.save(commit=False)
            component.mixture = mixture

            # التحقق من أن مجموع النسب لا يتجاوز 100%
            current_total = mixture.total_components_percentage
            if current_total + component.percentage > 100:
                messages.error(request,
                    f'لا يمكن إضافة هذا المكون. مجموع النسب سيصبح {current_total + component.percentage}% '
                    f'والحد الأقصى هو 100%')
            else:
                component.save()
                mixture.calculate_cost()  # إعادة حساب التكلفة
                messages.success(request, f'تم إضافة المكون "{component.ingredient.name}" بنجاح')
                return redirect('mixtures:detail', pk=mixture.pk)
    else:
        form = MixtureComponentForm(mixture=mixture)

    context = {
        'form': form,
        'mixture': mixture,
        'title': f'إضافة مكون للخلطة: {mixture.name}',
        'remaining_percentage': 100 - mixture.total_components_percentage
    }
    return render(request, 'mixtures/component_form.html', context)


@login_required
def mixture_component_edit(request, mixture_pk, component_pk):
    """تعديل مكون في الخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=mixture_pk)
    component = get_object_or_404(MixtureComponent, pk=component_pk, mixture=mixture)

    if request.method == 'POST':
        form = MixtureComponentForm(request.POST, instance=component, mixture=mixture)
        if form.is_valid():
            # التحقق من أن مجموع النسب لا يتجاوز 100%
            current_total = mixture.total_components_percentage - component.percentage
            new_percentage = form.cleaned_data['percentage']

            if current_total + new_percentage > 100:
                messages.error(request,
                    f'لا يمكن تحديث هذا المكون. مجموع النسب سيصبح {current_total + new_percentage}% '
                    f'والحد الأقصى هو 100%')
            else:
                form.save()
                mixture.calculate_cost()  # إعادة حساب التكلفة
                messages.success(request, f'تم تحديث المكون "{component.ingredient.name}" بنجاح')
                return redirect('mixtures:detail', pk=mixture.pk)
    else:
        form = MixtureComponentForm(instance=component, mixture=mixture)

    context = {
        'form': form,
        'mixture': mixture,
        'component': component,
        'title': f'تعديل المكون: {component.ingredient.name}',
        'remaining_percentage': 100 - mixture.total_components_percentage + component.percentage
    }
    return render(request, 'mixtures/component_form.html', context)


@login_required
def mixture_component_delete(request, mixture_pk, component_pk):
    """حذف مكون من الخلطة"""
    mixture = get_object_or_404(FeedMixture, pk=mixture_pk)
    component = get_object_or_404(MixtureComponent, pk=component_pk, mixture=mixture)

    if request.method == 'POST':
        ingredient_name = component.ingredient.name
        component.delete()
        mixture.calculate_cost()  # إعادة حساب التكلفة
        messages.success(request, f'تم حذف المكون "{ingredient_name}" من الخلطة')
        return redirect('mixtures:detail', pk=mixture.pk)

    context = {
        'mixture': mixture,
        'component': component,
        'percentage_after_deletion': mixture.total_components_percentage - component.percentage
    }
    return render(request, 'mixtures/component_delete.html', context)
