{% extends 'base.html' %}

{% block title %}تفاصيل الطلب: {{ order.order_number }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبات</a></li>
        <li class="breadcrumb-item active">{{ order.order_number }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-shopping-cart"></i>
                تفاصيل الطلب: {{ order.order_number }}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'orders:edit' order.pk %}" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i>
                    تعديل
                </a>
                {% if order.invoice %}
                    <a href="{% url 'invoices:detail' order.invoice.pk %}" class="btn btn-outline-success">
                        <i class="fas fa-file-invoice"></i>
                        عرض الفاتورة
                    </a>
                {% else %}
                    <a href="{% url 'invoices:create_from_order' order.pk %}" class="btn btn-outline-info">
                        <i class="fas fa-file-invoice"></i>
                        إنشاء فاتورة
                    </a>
                {% endif %}
                <a href="{% url 'orders:item_add' order.pk %}" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    إضافة عنصر
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الطلب -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم الطلب:</strong> {{ order.order_number }}</p>
                        <p><strong>العميل:</strong>
                            <a href="{% url 'orders:customer_detail' order.customer.pk %}" class="text-decoration-none">
                                {{ order.customer.name }}
                            </a>
                            {% if order.customer.company_name %}
                            <br><small class="text-muted">{{ order.customer.company_name }}</small>
                            {% endif %}
                        </p>
                        <p><strong>تاريخ الطلب:</strong> {{ order.order_date|date:"Y/m/d H:i" }}</p>
                        <p><strong>التاريخ المطلوب:</strong> {{ order.required_date|date:"Y/m/d" }}</p>
                        {% if order.delivery_date %}
                        <p><strong>تاريخ التسليم:</strong> {{ order.delivery_date|date:"Y/m/d H:i" }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <p><strong>حالة الطلب:</strong>
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">مؤكد</span>
                            {% elif order.status == 'in_production' %}
                                <span class="badge bg-primary">قيد الإنتاج</span>
                            {% elif order.status == 'ready' %}
                                <span class="badge bg-secondary">جاهز</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </p>
                        <p><strong>حالة الدفع:</strong>
                            {% if order.payment_status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوع</span>
                            {% elif order.payment_status == 'partial' %}
                                <span class="badge bg-warning">مدفوع جزئياً</span>
                            {% elif order.payment_status == 'paid' %}
                                <span class="badge bg-success">مدفوع بالكامل</span>
                            {% endif %}
                        </p>
                        <p><strong>أنشئ بواسطة:</strong> {{ order.created_by.get_full_name|default:order.created_by.username }}</p>
                        <p><strong>آخر تحديث:</strong> {{ order.updated_at|date:"Y/m/d H:i" }}</p>
                    </div>
                </div>

                {% if order.delivery_address %}
                <hr>
                <p><strong>عنوان التسليم:</strong></p>
                <p class="text-muted">{{ order.delivery_address }}</p>
                {% endif %}

                {% if order.notes %}
                <hr>
                <p><strong>ملاحظات:</strong></p>
                <p class="text-muted">{{ order.notes }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- ملخص مالي -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator"></i>
                    الملخص المالي
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>المجموع الفرعي:</span>
                    <span class="currency">{{ order.subtotal|floatformat:3 }} د.ع</span>
                </div>
                {% if order.discount_percentage > 0 %}
                <div class="d-flex justify-content-between mb-2">
                    <span>الخصم ({{ order.discount_percentage }}%):</span>
                    <span class="currency text-success">-{{ order.discount_amount|floatformat:3 }} د.ع</span>
                </div>
                {% endif %}
                <hr>
                <div class="d-flex justify-content-between mb-3">
                    <strong>المبلغ الإجمالي:</strong>
                    <strong class="currency">{{ order.total_amount|floatformat:3 }} د.ع</strong>
                </div>

                <div class="d-flex justify-content-between mb-2">
                    <span>المبلغ المدفوع:</span>
                    <span class="currency text-success">{{ order.paid_amount|floatformat:3 }} د.ع</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>المبلغ المتبقي:</span>
                    <span class="currency {% if order.remaining_amount > 0 %}text-danger{% else %}text-success{% endif %}">
                        {{ order.remaining_amount|floatformat:3 }} د.ع
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- عناصر الطلب -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-list"></i>
                عناصر الطلب ({{ items.count }} عنصر)
            </h6>
            <a href="{% url 'orders:item_add' order.pk %}" class="btn btn-sm btn-success">
                <i class="fas fa-plus"></i>
                إضافة عنصر
            </a>
        </div>
    </div>
    <div class="card-body">
        {% if items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نوع الخلطة</th>
                        <th>الكمية (كغم)</th>
                        <th>سعر الوحدة (د.ع/كغم)</th>
                        <th>المبلغ الإجمالي</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ item.mixture.name }}</strong>
                                <br><small class="text-muted">{{ item.mixture.feed_type.name }}</small>
                            </div>
                        </td>
                        <td>{{ item.quantity_kg|floatformat:2 }}</td>
                        <td class="currency">{{ item.unit_price|floatformat:3 }}</td>
                        <td><strong class="currency">{{ item.total_price|floatformat:3 }} د.ع</strong></td>
                        <td>
                            {% if item.notes %}
                                <small class="text-muted">{{ item.notes|truncatechars:50 }}</small>
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'orders:item_edit' order.pk item.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'orders:item_delete' order.pk item.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th>الإجمالي</th>
                        <th>{{ items|length }} عنصر</th>
                        <th>-</th>
                        <th><strong class="currency">{{ order.subtotal|floatformat:3 }} د.ع</strong></th>
                        <th>-</th>
                        <th>-</th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عناصر في هذا الطلب</h5>
            <p class="text-muted">ابدأ بإضافة عناصر للطلب</p>
            <a href="{% url 'orders:item_add' order.pk %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة عنصر جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'orders:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة لقائمة الطلبات
        </a>
    </div>
</div>
{% endblock %}
