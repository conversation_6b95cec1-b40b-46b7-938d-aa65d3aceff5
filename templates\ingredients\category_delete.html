{% extends 'base.html' %}

{% block title %}حذف فئة المكونات - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:list' %}">المكونات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'ingredients:categories' %}">فئات المكونات</a></li>
        <li class="breadcrumb-item active">حذف فئة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد حذف الفئة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
                
                <p>هل أنت متأكد من حذف فئة المكونات التالية؟</p>
                
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-tag"></i>
                            {{ category.name }}
                        </h6>
                        {% if category.description %}
                            <p class="card-text">{{ category.description }}</p>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <strong>عدد المكونات:</strong>
                                <span class="badge bg-primary">{{ category.ingredients.count }}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ الإنشاء:</strong>
                                {{ category.created_at|date:"d/m/Y" }}
                            </div>
                        </div>
                        
                        {% if category.ingredients.exists %}
                            <hr>
                            <h6>المكونات التي سيتم حذفها:</h6>
                            <div class="max-height-150 overflow-auto">
                                {% for ingredient in category.ingredients.all %}
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>{{ ingredient.name }}</span>
                                        <span class="badge bg-light text-dark">{{ ingredient.current_stock|floatformat:0 }} كغم</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if category.ingredients.exists %}
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>ملاحظة مهمة:</strong> 
                    سيتم حذف {{ category.ingredients.count }} مكون مرتبط بهذه الفئة أيضاً.
                </div>
                {% endif %}
                
                <form method="post" class="mt-4">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'ingredients:categories' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.max-height-150 {
    max-height: 150px;
}
</style>
{% endblock %}
