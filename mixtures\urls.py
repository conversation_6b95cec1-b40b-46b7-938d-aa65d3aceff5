from django.urls import path
from . import views

app_name = 'mixtures'

urlpatterns = [
    # خلطات الأعلاف
    path('', views.mixture_list, name='list'),
    path('add/', views.mixture_add, name='add'),
    path('<int:pk>/', views.mixture_detail, name='detail'),
    path('<int:pk>/edit/', views.mixture_edit, name='edit'),
    path('<int:pk>/delete/', views.mixture_delete, name='delete'),
    path('<int:pk>/calculate/', views.mixture_calculate, name='calculate'),

    # مكونات الخلطة
    path('<int:mixture_pk>/components/add/', views.mixture_component_add, name='component_add'),
    path('<int:mixture_pk>/components/<int:component_pk>/edit/', views.mixture_component_edit, name='component_edit'),
    path('<int:mixture_pk>/components/<int:component_pk>/delete/', views.mixture_component_delete, name='component_delete'),

    # أنواع الأعلاف
    path('types/', views.feed_type_list, name='types'),
    path('types/add/', views.feed_type_add, name='type_add'),
]
