# Generated by Django 5.2.1 on 2025-06-03 21:05

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mixtures', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='feedmixture',
            name='cost_per_kg',
            field=models.DecimalField(decimal_places=3, default=0, editable=False, max_digits=10, verbose_name='تكلفة الإنتاج لكل كغم (دينار أردني)'),
        ),
        migrations.AlterField(
            model_name='feedmixture',
            name='selling_price_per_kg',
            field=models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر البيع لكل كغم (دينار أردني)'),
        ),
    ]
