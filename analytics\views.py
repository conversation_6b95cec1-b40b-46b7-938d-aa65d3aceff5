from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse
from django.db.models import Sum, Count, Min, Max
from django.utils import timezone
from .models import ProductionBatch
from orders.models import Order, Customer


@login_required
def analytics_dashboard(request):
    """لوحة تحكم التحليلات"""
    from django.db.models import Avg, F
    from orders.models import OrderItem
    from mixtures.models import FeedMixture

    # إحصائيات الإنتاج
    production_stats = {
        'total_batches': ProductionBatch.objects.count(),
        'total_production': ProductionBatch.objects.aggregate(
            total=Sum('quantity_produced_kg')
        )['total'] or 0,
        'total_cost': ProductionBatch.objects.aggregate(
            total=Sum('total_cost')
        )['total'] or 0,
    }

    # إحصائيات المبيعات
    sales_stats = {
        'total_orders': Order.objects.count(),
        'total_revenue': Order.objects.aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'pending_orders': Order.objects.filter(status='pending').count(),
    }

    # أحدث دفعات الإنتاج
    recent_batches = ProductionBatch.objects.select_related('mixture', 'produced_by').order_by('-production_date')[:5]

    # أفضل المنتجات مبيعاً
    top_products = OrderItem.objects.select_related('mixture').values(
        'mixture__name'
    ).annotate(
        name=F('mixture__name'),
        total_sold=Sum('quantity_kg'),
        total_revenue=Sum(F('quantity_kg') * F('unit_price'))
    ).order_by('-total_sold')[:5]

    context = {
        'production_stats': production_stats,
        'sales_stats': sales_stats,
        'recent_batches': recent_batches,
        'top_products': top_products,
    }

    return render(request, 'analytics/dashboard.html', context)


@login_required
def production_list(request):
    """قائمة دفعات الإنتاج"""
    from django.db.models import Avg

    batches = ProductionBatch.objects.select_related('mixture', 'mixture__feed_type', 'produced_by').order_by('-production_date')

    # حساب الإحصائيات
    total_production = batches.aggregate(total=Sum('quantity_produced_kg'))['total'] or 0
    total_cost = batches.aggregate(total=Sum('total_cost'))['total'] or 0
    # حساب متوسط التكلفة لكل كيلو
    avg_cost_per_kg = total_cost / total_production if total_production > 0 else 0

    context = {
        'batches': batches,
        'total_production': total_production,
        'total_cost': total_cost,
        'avg_cost_per_kg': avg_cost_per_kg,
    }

    return render(request, 'analytics/production.html', context)


@login_required
def production_add(request):
    """إضافة دفعة إنتاج جديدة"""
    if request.method == 'POST':
        messages.success(request, 'تم إضافة دفعة الإنتاج بنجاح')
        return redirect('analytics:production')

    from mixtures.models import FeedMixture
    mixtures = FeedMixture.objects.filter(is_active=True)
    return render(request, 'analytics/production_form.html', {'mixtures': mixtures})


@login_required
def sales_report(request):
    """تقرير المبيعات"""
    from django.db.models import Avg, F
    from orders.models import OrderItem, Customer
    from datetime import datetime, timedelta
    import json

    # تصفية حسب الفترة
    period = request.GET.get('period', 'month')
    today = datetime.now().date()

    if period == 'today':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today.replace(day=1)
        end_date = today
    elif period == 'year':
        start_date = today.replace(month=1, day=1)
        end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # إحصائيات المبيعات
    orders = Order.objects.filter(order_date__range=[start_date, end_date])
    total_sales = orders.aggregate(total=Sum('total_amount'))['total'] or 0
    total_orders = orders.count()
    avg_order_value = total_sales / total_orders if total_orders > 0 else 0
    total_customers = orders.values('customer').distinct().count()

    # أفضل المنتجات مبيعاً
    top_products = OrderItem.objects.filter(
        order__order_date__range=[start_date, end_date]
    ).select_related('mixture').values(
        'mixture__name'
    ).annotate(
        name=F('mixture__name'),
        total_sold=Sum('quantity_kg'),
        total_revenue=Sum(F('quantity_kg') * F('unit_price'))
    ).order_by('-total_sold')[:5]

    # أفضل العملاء
    top_customers = Customer.objects.filter(
        orders__order_date__range=[start_date, end_date]
    ).annotate(
        total_orders=Count('orders'),
        total_spent=Sum('orders__total_amount')
    ).order_by('-total_spent')[:5]

    # بيانات الرسم البياني
    sales_data = []
    sales_labels = []

    if period == 'month':
        # بيانات يومية للشهر الحالي
        for i in range((end_date - start_date).days + 1):
            date = start_date + timedelta(days=i)
            daily_sales = Order.objects.filter(order_date=date).aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            sales_data.append(float(daily_sales))
            sales_labels.append(date.strftime('%d/%m'))

    context = {
        'total_sales': total_sales,
        'total_orders': total_orders,
        'avg_order_value': avg_order_value,
        'total_customers': total_customers,
        'top_products': top_products,
        'top_customers': top_customers,
        'sales_data': json.dumps(sales_data),
        'sales_labels': json.dumps(sales_labels),
        'period': period,
    }

    return render(request, 'analytics/sales_report.html', context)


@login_required
def export_pdf_report(request):
    """تصدير التقارير كـ PDF"""
    from django.template.loader import get_template
    from django.http import HttpResponse

    # جمع البيانات للتقرير
    sales_revenue = Order.objects.aggregate(total=Sum('total_amount'))['total'] or 0
    sales_orders = Order.objects.count()
    production_total = ProductionBatch.objects.aggregate(total=Sum('quantity_produced_kg'))['total'] or 0
    production_batches = ProductionBatch.objects.count()

    # حساب المتوسطات
    avg_order_value = sales_revenue / sales_orders if sales_orders > 0 else 0
    avg_batch_size = production_total / production_batches if production_batches > 0 else 0

    context = {
        'company_name': 'مصنع الأعلاف',
        'report_date': timezone.now().date(),
        'sales_stats': {
            'total_revenue': sales_revenue,
            'total_orders': sales_orders,
            'avg_order_value': avg_order_value,
        },
        'production_stats': {
            'total_production': production_total,
            'total_batches': production_batches,
            'avg_batch_size': avg_batch_size,
        }
    }

    # تحميل القالب
    try:
        template = get_template('analytics/pdf_report.html')
        html_string = template.render(context)
    except Exception as e:
        # في حالة وجود خطأ في القالب، إنشاء HTML بسيط
        html_string = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير مصنع الأعلاف</title>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; text-align: right; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .stats {{ margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{context['company_name']}</h1>
                <h2>التقرير الشامل</h2>
                <p>تاريخ التقرير: {context['report_date']}</p>
            </div>

            <div class="stats">
                <h3>إحصائيات المبيعات</h3>
                <p>إجمالي الإيرادات: {context['sales_stats']['total_revenue']:.2f} د.أ</p>
                <p>إجمالي الطلبات: {context['sales_stats']['total_orders']}</p>
                <p>متوسط قيمة الطلب: {context['sales_stats']['avg_order_value']:.2f} د.أ</p>
            </div>

            <div class="stats">
                <h3>إحصائيات الإنتاج</h3>
                <p>إجمالي الإنتاج: {context['production_stats']['total_production']:.0f} كغم</p>
                <p>عدد دفعات الإنتاج: {context['production_stats']['total_batches']}</p>
                <p>متوسط حجم الدفعة: {context['production_stats']['avg_batch_size']:.0f} كغم</p>
            </div>
        </body>
        </html>
        """

    # إنشاء PDF (إذا كان WeasyPrint متاحاً ومثبت بشكل صحيح)
    try:
        from weasyprint import HTML, CSS
        html = HTML(string=html_string, base_url=request.build_absolute_uri())
        css = CSS(string='''
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
            body {
                font-family: 'Noto Sans Arabic', Arial, sans-serif;
                direction: rtl;
                text-align: right;
            }
        ''')
        pdf = html.write_pdf(stylesheets=[css])

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="report_{timezone.now().strftime("%Y%m%d")}.pdf"'
        return response

    except (ImportError, OSError, Exception) as e:
        # إذا لم يكن WeasyPrint متاحاً أو لم يعمل بشكل صحيح، إرجاع HTML
        response = HttpResponse(html_string, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="report_{timezone.now().strftime("%Y%m%d")}.html"'
        return response


@login_required
def export_excel_report(request):
    """تصدير التقارير كـ Excel"""
    import io
    from django.http import HttpResponse

    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill

        # إنشاء workbook جديد
        wb = openpyxl.Workbook()

        # ورقة المبيعات
        ws_sales = wb.active
        ws_sales.title = "تقرير المبيعات"

        # إعداد الخطوط والتنسيق
        header_font = Font(bold=True, size=14)
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")

        # عناوين الأعمدة
        headers = ['رقم الطلب', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة']
        for col, header in enumerate(headers, 1):
            cell = ws_sales.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment

        # بيانات المبيعات
        orders = Order.objects.select_related('customer').all()
        for row, order in enumerate(orders, 2):
            ws_sales.cell(row=row, column=1, value=order.order_number)
            ws_sales.cell(row=row, column=2, value=order.customer.name)
            ws_sales.cell(row=row, column=3, value=order.order_date.strftime('%Y-%m-%d'))
            ws_sales.cell(row=row, column=4, value=float(order.total_amount))
            ws_sales.cell(row=row, column=5, value=order.get_status_display())

        # ورقة الإنتاج
        ws_production = wb.create_sheet("تقرير الإنتاج")

        # عناوين الأعمدة للإنتاج
        prod_headers = ['رقم الدفعة', 'الخلطة', 'الكمية المنتجة', 'تاريخ الإنتاج', 'التكلفة']
        for col, header in enumerate(prod_headers, 1):
            cell = ws_production.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment

        # بيانات الإنتاج
        batches = ProductionBatch.objects.select_related('mixture').all()
        for row, batch in enumerate(batches, 2):
            ws_production.cell(row=row, column=1, value=batch.batch_number)
            ws_production.cell(row=row, column=2, value=batch.mixture.name)
            ws_production.cell(row=row, column=3, value=float(batch.quantity_produced_kg))
            ws_production.cell(row=row, column=4, value=batch.production_date.strftime('%Y-%m-%d'))
            ws_production.cell(row=row, column=5, value=float(batch.total_cost))

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # إرجاع الاستجابة
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="report_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        return response

    except ImportError:
        # إذا لم تكن openpyxl متاحة، إرجاع CSV
        import csv

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="report_{timezone.now().strftime("%Y%m%d")}.csv"'
        response.write('\ufeff')  # BOM for UTF-8

        writer = csv.writer(response)
        writer.writerow(['رقم الطلب', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة'])

        orders = Order.objects.select_related('customer').all()
        for order in orders:
            writer.writerow([
                order.order_number,
                order.customer.name,
                order.order_date.strftime('%Y-%m-%d'),
                float(order.total_amount),
                order.get_status_display()
            ])

        return response


@login_required
def advanced_reports(request):
    """التقارير المتقدمة"""
    from .reports import ReportGenerator
    from datetime import datetime

    # الحصول على التواريخ من الطلب
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    if end_date:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # الفلاتر
    filters = {}
    if request.GET.get('customer'):
        filters['customer'] = request.GET.get('customer')

    # إنشاء التقرير
    report_generator = ReportGenerator(start_date, end_date, filters)
    report_data = report_generator.generate_comprehensive_report()

    # قائمة العملاء للفلتر
    customers = Customer.objects.filter(is_active=True).order_by('name')

    context = {
        'report_data': report_data,
        'customers': customers,
        'start_date': start_date,
        'end_date': end_date,
        'filters': filters,
    }

    return render(request, 'analytics/advanced_reports.html', context)


@login_required
def export_advanced_report(request, format_type):
    """تصدير التقرير المتقدم"""
    from .reports import ReportGenerator
    from datetime import datetime
    import json

    # الحصول على التواريخ والفلاتر
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    if end_date:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    filters = {}
    if request.GET.get('customer'):
        filters['customer'] = request.GET.get('customer')

    # إنشاء التقرير
    report_generator = ReportGenerator(start_date, end_date, filters)
    report_data = report_generator.generate_comprehensive_report()

    if format_type == 'pdf':
        return export_report_pdf(report_data)
    elif format_type == 'excel':
        return export_report_excel(report_data)
    elif format_type == 'json':
        return export_report_json(report_data)
    else:
        return HttpResponse('تنسيق غير مدعوم', status=400)


def export_report_pdf(report_data):
    """تصدير التقرير كـ PDF"""
    from django.template.loader import get_template
    from django.http import HttpResponse

    # تحضير السياق للقالب
    context = {
        'report': report_data,
        'company_name': 'مصنع الأعلاف',
        'generated_at': timezone.now(),
    }

    # تحميل القالب
    template = get_template('analytics/advanced_report_pdf.html')
    html_string = template.render(context)

    # إنشاء PDF
    try:
        from weasyprint import HTML, CSS
        html = HTML(string=html_string)
        css = CSS(string='''
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
            body {
                font-family: 'Noto Sans Arabic', Arial, sans-serif;
                direction: rtl;
                text-align: right;
                font-size: 12px;
            }
            .page-break { page-break-before: always; }
        ''')
        pdf = html.write_pdf(stylesheets=[css])

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="advanced_report_{timezone.now().strftime("%Y%m%d")}.pdf"'
        return response

    except (ImportError, OSError, Exception) as e:
        # إذا لم يكن WeasyPrint متاحاً أو لم يعمل بشكل صحيح
        response = HttpResponse(html_string, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="advanced_report_{timezone.now().strftime("%Y%m%d")}.html"'
        return response


def export_report_excel(report_data):
    """تصدير التقرير كـ Excel"""
    import io
    from django.http import HttpResponse

    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

        # إنشاء workbook
        wb = openpyxl.Workbook()

        # إزالة الورقة الافتراضية
        wb.remove(wb.active)

        # ورقة الملخص العام
        ws_summary = wb.create_sheet("الملخص العام")
        create_summary_sheet(ws_summary, report_data)

        # ورقة المبيعات
        ws_sales = wb.create_sheet("تفاصيل المبيعات")
        create_sales_sheet(ws_sales, report_data['sales_summary'])

        # ورقة الإنتاج
        ws_production = wb.create_sheet("تفاصيل الإنتاج")
        create_production_sheet(ws_production, report_data['production_summary'])

        # ورقة العملاء
        ws_customers = wb.create_sheet("تحليل العملاء")
        create_customers_sheet(ws_customers, report_data['customer_analysis'])

        # حفظ الملف
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="advanced_report_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        return response

    except ImportError:
        # إذا لم تكن openpyxl متاحة
        import csv

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="advanced_report_{timezone.now().strftime("%Y%m%d")}.csv"'
        response.write('\ufeff')  # BOM for UTF-8

        writer = csv.writer(response)

        # كتابة بيانات المبيعات
        writer.writerow(['=== ملخص المبيعات ==='])
        writer.writerow(['إجمالي الطلبات', report_data['sales_summary']['total_orders']])
        writer.writerow(['إجمالي الإيرادات', report_data['sales_summary']['total_revenue']])
        writer.writerow(['متوسط قيمة الطلب', report_data['sales_summary']['avg_order_value']])

        return response


def export_report_json(report_data):
    """تصدير التقرير كـ JSON"""
    from django.http import JsonResponse

    # تحويل التواريخ إلى strings
    def date_handler(obj):
        if hasattr(obj, 'isoformat'):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    response = JsonResponse(report_data, json_dumps_params={'default': date_handler, 'ensure_ascii': False})
    response['Content-Disposition'] = f'attachment; filename="advanced_report_{timezone.now().strftime("%Y%m%d")}.json"'
    return response


def create_summary_sheet(worksheet, report_data):
    """إنشاء ورقة الملخص العام"""
    try:
        from openpyxl.styles import Font
        # إعداد التنسيق
        header_font = Font(bold=True, size=14)
    except ImportError:
        header_font = None

    row = 1

    # معلومات التقرير
    worksheet.cell(row=row, column=1, value="التقرير الشامل - مصنع الأعلاف").font = header_font
    row += 2

    # فترة التقرير
    worksheet.cell(row=row, column=1, value="فترة التقرير:")
    worksheet.cell(row=row, column=2, value=f"من {report_data['period']['start_date']} إلى {report_data['period']['end_date']}")
    row += 2

    # ملخص المبيعات
    worksheet.cell(row=row, column=1, value="ملخص المبيعات").font = header_font
    row += 1
    worksheet.cell(row=row, column=1, value="إجمالي الطلبات:")
    worksheet.cell(row=row, column=2, value=report_data['sales_summary']['total_orders'])
    row += 1
    worksheet.cell(row=row, column=1, value="إجمالي الإيرادات:")
    worksheet.cell(row=row, column=2, value=report_data['sales_summary']['total_revenue'])
    row += 2

    # ملخص الإنتاج
    worksheet.cell(row=row, column=1, value="ملخص الإنتاج").font = header_font
    row += 1
    worksheet.cell(row=row, column=1, value="إجمالي دفعات الإنتاج:")
    worksheet.cell(row=row, column=2, value=report_data['production_summary']['total_batches'])
    row += 1
    worksheet.cell(row=row, column=1, value="إجمالي الإنتاج (كغم):")
    worksheet.cell(row=row, column=2, value=report_data['production_summary']['total_production'])


def create_sales_sheet(worksheet, sales_data):
    """إنشاء ورقة تفاصيل المبيعات"""
    try:
        from openpyxl.styles import Font
        header_font = Font(bold=True)
    except ImportError:
        header_font = None

    # العناوين
    headers = ['المؤشر', 'القيمة']
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        if header_font:
            cell.font = header_font

    # البيانات
    data = [
        ('إجمالي الطلبات', sales_data['total_orders']),
        ('إجمالي الإيرادات', sales_data['total_revenue']),
        ('متوسط قيمة الطلب', sales_data['avg_order_value']),
        ('معدل النمو (%)', sales_data['growth_rate']),
    ]

    for row, (label, value) in enumerate(data, 2):
        worksheet.cell(row=row, column=1, value=label)
        worksheet.cell(row=row, column=2, value=value)


def create_production_sheet(worksheet, production_data):
    """إنشاء ورقة تفاصيل الإنتاج"""
    try:
        from openpyxl.styles import Font
        header_font = Font(bold=True)
    except ImportError:
        header_font = None

    # العناوين
    headers = ['المؤشر', 'القيمة']
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        if header_font:
            cell.font = header_font

    # البيانات
    data = [
        ('إجمالي دفعات الإنتاج', production_data['total_batches']),
        ('إجمالي الإنتاج (كغم)', production_data['total_production']),
        ('إجمالي التكلفة', production_data['total_cost']),
        ('متوسط حجم الدفعة', production_data['avg_batch_size']),
        ('تكلفة الكيلو', production_data['cost_per_kg']),
    ]

    for row, (label, value) in enumerate(data, 2):
        worksheet.cell(row=row, column=1, value=label)
        worksheet.cell(row=row, column=2, value=value)


def create_customers_sheet(worksheet, customers_data):
    """إنشاء ورقة تحليل العملاء"""
    try:
        from openpyxl.styles import Font
        header_font = Font(bold=True)
    except ImportError:
        header_font = None

    # العناوين
    headers = ['اسم العميل', 'عدد الطلبات', 'إجمالي المشتريات', 'متوسط قيمة الطلب']
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        if header_font:
            cell.font = header_font

    # بيانات أفضل العملاء
    for row, customer in enumerate(customers_data['top_customers'], 2):
        worksheet.cell(row=row, column=1, value=customer['name'])
        worksheet.cell(row=row, column=2, value=customer['orders_count'])
        worksheet.cell(row=row, column=3, value=customer['total_spent'])
        worksheet.cell(row=row, column=4, value=customer['avg_order_value'])


@login_required
def export_pdf(request):
    """تصدير التقرير كـ PDF"""
    # سيتم تطوير هذا لاحقاً
    messages.info(request, 'ميزة تصدير PDF قيد التطوير')
    return redirect('analytics:dashboard')


@login_required
def export_excel(request):
    """تصدير التقرير كـ Excel"""
    # سيتم تطوير هذا لاحقاً
    messages.info(request, 'ميزة تصدير Excel قيد التطوير')
    return redirect('analytics:dashboard')
