// تحسينات JavaScript للتطبيق

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    
    // إضافة تأثيرات الأنيميشن
    addAnimations();
    
    // تحسين النماذج
    enhanceForms();
    
    // تحسين الجداول
    enhanceTables();
    
    // تحسين الأزرار
    enhanceButtons();
    
    // إضافة مؤشرات التحميل
    addLoadingIndicators();
    
    // تحسين الإشعارات
    enhanceAlerts();
    
    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();
    
    // تحسين البحث المباشر
    enhanceSearch();
});

// إضافة تأثيرات الأنيميشن
function addAnimations() {
    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // مراقبة الكروت والعناصر المهمة
    document.querySelectorAll('.card, .stat-card, .table').forEach(el => {
        observer.observe(el);
    });
}

// تحسين النماذج
function enhanceForms() {
    // إضافة تأثيرات للحقول
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // التحقق من صحة البيانات فوراً
        input.addEventListener('input', function() {
            validateField(this);
        });
    });
    
    // تحسين أزرار الإرسال
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الحفظ...';
                submitBtn.disabled = true;
            }
        });
    });
}

// التحقق من صحة الحقول
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    let isValid = true;
    let message = '';
    
    // التحقق من الحقول المطلوبة
    if (field.required && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'البريد الإلكتروني غير صحيح';
        }
    }
    
    // التحقق من الأرقام
    if (fieldType === 'number' && value) {
        if (isNaN(value) || parseFloat(value) < 0) {
            isValid = false;
            message = 'يجب أن يكون رقماً موجباً';
        }
    }
    
    // عرض النتيجة
    showFieldValidation(field, isValid, message);
}

// عرض نتيجة التحقق
function showFieldValidation(field, isValid, message) {
    // إزالة الرسائل السابقة
    const existingFeedback = field.parentElement.querySelector('.invalid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // إضافة أو إزالة الكلاسات
    field.classList.remove('is-valid', 'is-invalid');
    
    if (!isValid) {
        field.classList.add('is-invalid');
        
        // إضافة رسالة الخطأ
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        field.parentElement.appendChild(feedback);
    } else if (field.value.trim()) {
        field.classList.add('is-valid');
    }
}

// تحسين الجداول
function enhanceTables() {
    document.querySelectorAll('.table').forEach(table => {
        // إضافة تأثيرات الصفوف
        table.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
        
        // إضافة ترقيم تلقائي
        const tbody = table.querySelector('tbody');
        if (tbody && !table.classList.contains('no-numbering')) {
            tbody.querySelectorAll('tr').forEach((row, index) => {
                const firstCell = row.querySelector('td');
                if (firstCell && !firstCell.textContent.match(/^\d+$/)) {
                    const numberCell = document.createElement('td');
                    numberCell.textContent = index + 1;
                    numberCell.className = 'text-muted';
                    row.insertBefore(numberCell, firstCell);
                }
            });
        }
    });
}

// تحسين الأزرار
function enhanceButtons() {
    document.querySelectorAll('.btn').forEach(btn => {
        // إضافة تأثير الموجة
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// إضافة مؤشرات التحميل
function addLoadingIndicators() {
    // مؤشر التحميل للروابط الخارجية
    document.querySelectorAll('a[href*="export"], a[href*="download"]').forEach(link => {
        link.addEventListener('click', function() {
            showLoadingToast('جاري التحضير...');
        });
    });
}

// تحسين الإشعارات
function enhanceAlerts() {
    // إضافة إمكانية الإغلاق التلقائي
    document.querySelectorAll('.alert').forEach(alert => {
        if (!alert.querySelector('.btn-close')) {
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'btn-close';
            closeBtn.setAttribute('data-bs-dismiss', 'alert');
            alert.appendChild(closeBtn);
        }
        
        // إغلاق تلقائي بعد 5 ثوان للإشعارات العادية
        if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        }
    });
}

// إضافة اختصارات لوحة المفاتيح
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
        
        // Ctrl + N لإضافة جديد
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const addBtn = document.querySelector('a[href*="add"], a[href*="create"]');
            if (addBtn) {
                window.location.href = addBtn.href;
            }
        }
        
        // ESC للإلغاء
        if (e.key === 'Escape') {
            const modal = document.querySelector('.modal.show');
            if (modal) {
                const closeBtn = modal.querySelector('.btn-close, [data-bs-dismiss="modal"]');
                if (closeBtn) closeBtn.click();
            }
        }
    });
}

// تحسين البحث المباشر
function enhanceSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[name*="search"]');
    
    searchInputs.forEach(input => {
        let searchTimeout;
        
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performLiveSearch(query, this);
                }, 300);
            }
        });
    });
}

// تنفيذ البحث المباشر
function performLiveSearch(query, input) {
    const resultsContainer = input.parentElement.querySelector('.search-results');
    if (!resultsContainer) return;
    
    // عرض مؤشر التحميل
    resultsContainer.innerHTML = '<div class="text-center p-3"><div class="loading-spinner"></div></div>';
    
    // محاكاة البحث (يمكن استبدالها بطلب AJAX حقيقي)
    setTimeout(() => {
        resultsContainer.innerHTML = `
            <div class="list-group">
                <div class="list-group-item">نتيجة البحث 1 لـ "${query}"</div>
                <div class="list-group-item">نتيجة البحث 2 لـ "${query}"</div>
                <div class="list-group-item">نتيجة البحث 3 لـ "${query}"</div>
            </div>
        `;
    }, 500);
}

// عرض رسالة تحميل
function showLoadingToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast show position-fixed top-0 end-0 m-3';
    toast.innerHTML = `
        <div class="toast-body d-flex align-items-center">
            <div class="loading-spinner me-2"></div>
            ${message}
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// تحسين الاستجابة للهواتف
function enhanceMobileExperience() {
    // إضافة دعم اللمس للجداول
    document.querySelectorAll('.table-responsive').forEach(container => {
        let isScrolling = false;
        
        container.addEventListener('touchstart', () => {
            isScrolling = true;
        });
        
        container.addEventListener('touchend', () => {
            setTimeout(() => {
                isScrolling = false;
            }, 100);
        });
    });
}

// تحسين الأداء
function optimizePerformance() {
    // تأخير تحميل الصور
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تشغيل التحسينات الإضافية
enhanceMobileExperience();
optimizePerformance();
