from django.urls import path
from . import api_views

app_name = 'mixtures_api'

urlpatterns = [
    # أنواع الأعلاف
    path('feed-types/', api_views.FeedTypeListCreateView.as_view(), name='feed-type-list'),
    path('feed-types/<int:pk>/', api_views.FeedTypeDetailView.as_view(), name='feed-type-detail'),
    
    # الخلطات
    path('', api_views.FeedMixtureListCreateView.as_view(), name='mixture-list'),
    path('<int:pk>/', api_views.FeedMixtureDetailView.as_view(), name='mixture-detail'),
    path('<int:pk>/calculate/', api_views.calculate_mixture_cost, name='calculate-cost'),
    
    # مكونات الخلطة
    path('<int:mixture_id>/components/', api_views.MixtureComponentListCreateView.as_view(), name='component-list'),
    path('<int:mixture_id>/components/<int:pk>/', api_views.MixtureComponentDetailView.as_view(), name='component-detail'),
    
    # إحصائيات ووظائف إضافية
    path('stats/', api_views.mixture_stats, name='mixture-stats'),
    path('incomplete/', api_views.incomplete_mixtures, name='incomplete-mixtures'),
]
