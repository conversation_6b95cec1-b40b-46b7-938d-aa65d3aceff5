# Generated by Django 5.2.1 on 2025-06-03 21:05

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customer',
            name='credit_limit',
            field=models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='حد الائتمان (دينار أردني)'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='current_balance',
            field=models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الرصيد الحالي (دينار أردني)'),
        ),
    ]
