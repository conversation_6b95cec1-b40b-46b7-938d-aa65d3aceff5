"""
نظام البحث المتقدم
"""

from django.db.models import Q, Count, Sum, F
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from ingredients.models import Ingredient, IngredientCategory
from mixtures.models import FeedMixture, FeedType
from orders.models import Order, Customer
from invoices.models import Invoice
from analytics.models import ProductionBatch
import re


class AdvancedSearch:
    """فئة البحث المتقدم"""
    
    def __init__(self, query, search_type='all', filters=None):
        self.query = query.strip() if query else ''
        self.search_type = search_type
        self.filters = filters or {}
        self.results = {}
    
    def search(self):
        """تنفيذ البحث"""
        if not self.query:
            return self.get_empty_results()
        
        if self.search_type == 'all':
            return self.search_all()
        elif self.search_type == 'ingredients':
            return self.search_ingredients()
        elif self.search_type == 'mixtures':
            return self.search_mixtures()
        elif self.search_type == 'orders':
            return self.search_orders()
        elif self.search_type == 'customers':
            return self.search_customers()
        elif self.search_type == 'invoices':
            return self.search_invoices()
        else:
            return self.get_empty_results()
    
    def get_empty_results(self):
        """نتائج فارغة"""
        return {
            'ingredients': [],
            'mixtures': [],
            'orders': [],
            'customers': [],
            'invoices': [],
            'production_batches': [],
            'total_count': 0,
            'query': self.query,
            'search_type': self.search_type
        }
    
    def search_all(self):
        """البحث في جميع البيانات"""
        results = {
            'ingredients': self.search_ingredients()['ingredients'][:5],
            'mixtures': self.search_mixtures()['mixtures'][:5],
            'orders': self.search_orders()['orders'][:5],
            'customers': self.search_customers()['customers'][:5],
            'invoices': self.search_invoices()['invoices'][:5],
            'production_batches': self.search_production_batches()[:5],
            'query': self.query,
            'search_type': self.search_type
        }
        
        # حساب العدد الإجمالي
        results['total_count'] = sum([
            len(results['ingredients']),
            len(results['mixtures']),
            len(results['orders']),
            len(results['customers']),
            len(results['invoices']),
            len(results['production_batches'])
        ])
        
        return results
    
    def search_ingredients(self):
        """البحث في المكونات"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(name__icontains=part) |
                Q(description__icontains=part) |
                Q(category__name__icontains=part) |
                Q(supplier__name__icontains=part)
            )
        
        ingredients = Ingredient.objects.filter(search_query).select_related(
            'category', 'supplier'
        ).distinct()
        
        # تطبيق الفلاتر
        if self.filters.get('category'):
            ingredients = ingredients.filter(category_id=self.filters['category'])
        
        if self.filters.get('is_active') is not None:
            ingredients = ingredients.filter(is_active=self.filters['is_active'])
        
        if self.filters.get('low_stock'):
            ingredients = ingredients.filter(current_stock__lt=F('minimum_stock'))
        
        return {
            'ingredients': list(ingredients[:20]),
            'total_count': ingredients.count(),
            'query': self.query,
            'search_type': 'ingredients'
        }
    
    def search_mixtures(self):
        """البحث في خلطات الأعلاف"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(name__icontains=part) |
                Q(description__icontains=part) |
                Q(feed_type__name__icontains=part) |
                Q(components__ingredient__name__icontains=part)
            )
        
        mixtures = FeedMixture.objects.filter(search_query).select_related(
            'feed_type'
        ).prefetch_related('components__ingredient').distinct()
        
        # تطبيق الفلاتر
        if self.filters.get('feed_type'):
            mixtures = mixtures.filter(feed_type_id=self.filters['feed_type'])
        
        if self.filters.get('is_active') is not None:
            mixtures = mixtures.filter(is_active=self.filters['is_active'])
        
        return {
            'mixtures': list(mixtures[:20]),
            'total_count': mixtures.count(),
            'query': self.query,
            'search_type': 'mixtures'
        }
    
    def search_orders(self):
        """البحث في الطلبات"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(order_number__icontains=part) |
                Q(customer__name__icontains=part) |
                Q(customer__company_name__icontains=part) |
                Q(customer__phone__icontains=part) |
                Q(items__mixture__name__icontains=part)
            )
        
        orders = Order.objects.filter(search_query).select_related(
            'customer'
        ).prefetch_related('items__mixture').distinct()
        
        # تطبيق الفلاتر
        if self.filters.get('status'):
            orders = orders.filter(status=self.filters['status'])
        
        if self.filters.get('date_from'):
            orders = orders.filter(order_date__gte=self.filters['date_from'])
        
        if self.filters.get('date_to'):
            orders = orders.filter(order_date__lte=self.filters['date_to'])
        
        return {
            'orders': list(orders[:20]),
            'total_count': orders.count(),
            'query': self.query,
            'search_type': 'orders'
        }
    
    def search_customers(self):
        """البحث في العملاء"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(name__icontains=part) |
                Q(company_name__icontains=part) |
                Q(phone__icontains=part) |
                Q(email__icontains=part) |
                Q(address__icontains=part)
            )
        
        customers = Customer.objects.filter(search_query).annotate(
            total_orders=Count('orders'),
            total_spent=Sum('orders__total_amount')
        ).distinct()
        
        # تطبيق الفلاتر
        if self.filters.get('is_active') is not None:
            customers = customers.filter(is_active=self.filters['is_active'])
        
        return {
            'customers': list(customers[:20]),
            'total_count': customers.count(),
            'query': self.query,
            'search_type': 'customers'
        }
    
    def search_invoices(self):
        """البحث في الفواتير"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(invoice_number__icontains=part) |
                Q(customer__name__icontains=part) |
                Q(customer__company_name__icontains=part) |
                Q(order__order_number__icontains=part)
            )
        
        invoices = Invoice.objects.filter(search_query).select_related(
            'customer', 'order'
        ).distinct()
        
        # تطبيق الفلاتر
        if self.filters.get('status'):
            invoices = invoices.filter(status=self.filters['status'])
        
        if self.filters.get('date_from'):
            invoices = invoices.filter(invoice_date__gte=self.filters['date_from'])
        
        if self.filters.get('date_to'):
            invoices = invoices.filter(invoice_date__lte=self.filters['date_to'])
        
        return {
            'invoices': list(invoices[:20]),
            'total_count': invoices.count(),
            'query': self.query,
            'search_type': 'invoices'
        }
    
    def search_production_batches(self):
        """البحث في دفعات الإنتاج"""
        query_parts = self.query.split()
        search_query = Q()
        
        for part in query_parts:
            search_query |= (
                Q(batch_number__icontains=part) |
                Q(mixture__name__icontains=part) |
                Q(mixture__feed_type__name__icontains=part)
            )
        
        batches = ProductionBatch.objects.filter(search_query).select_related(
            'mixture', 'mixture__feed_type'
        ).distinct()
        
        return list(batches[:20])


class SearchSuggestions:
    """اقتراحات البحث"""
    
    @staticmethod
    def get_suggestions(query, limit=10):
        """الحصول على اقتراحات البحث"""
        if not query or len(query) < 2:
            return []
        
        suggestions = []
        
        # اقتراحات من أسماء المكونات
        ingredients = Ingredient.objects.filter(
            name__icontains=query,
            is_active=True
        ).values_list('name', flat=True)[:limit//4]
        suggestions.extend([{'text': name, 'type': 'ingredient'} for name in ingredients])
        
        # اقتراحات من أسماء الخلطات
        mixtures = FeedMixture.objects.filter(
            name__icontains=query,
            is_active=True
        ).values_list('name', flat=True)[:limit//4]
        suggestions.extend([{'text': name, 'type': 'mixture'} for name in mixtures])
        
        # اقتراحات من أسماء العملاء
        customers = Customer.objects.filter(
            Q(name__icontains=query) | Q(company_name__icontains=query),
            is_active=True
        ).values_list('name', flat=True)[:limit//4]
        suggestions.extend([{'text': name, 'type': 'customer'} for name in customers])
        
        # اقتراحات من أرقام الطلبات
        orders = Order.objects.filter(
            order_number__icontains=query
        ).values_list('order_number', flat=True)[:limit//4]
        suggestions.extend([{'text': number, 'type': 'order'} for number in orders])
        
        return suggestions[:limit]


class SearchFilters:
    """فلاتر البحث"""
    
    @staticmethod
    def get_ingredient_filters():
        """فلاتر المكونات"""
        return {
            'categories': IngredientCategory.objects.filter(is_active=True),
            'status_choices': [
                ('active', 'نشط'),
                ('inactive', 'غير نشط'),
            ],
            'stock_choices': [
                ('low', 'مخزون منخفض'),
                ('normal', 'مخزون طبيعي'),
            ]
        }
    
    @staticmethod
    def get_mixture_filters():
        """فلاتر الخلطات"""
        return {
            'feed_types': FeedType.objects.filter(is_active=True),
            'status_choices': [
                ('active', 'نشط'),
                ('inactive', 'غير نشط'),
            ]
        }
    
    @staticmethod
    def get_order_filters():
        """فلاتر الطلبات"""
        return {
            'status_choices': Order.STATUS_CHOICES,
        }
    
    @staticmethod
    def get_invoice_filters():
        """فلاتر الفواتير"""
        return {
            'status_choices': Invoice.STATUS_CHOICES,
        }


def highlight_search_term(text, query):
    """تمييز كلمات البحث في النص"""
    if not query or not text:
        return text
    
    # تقسيم الاستعلام إلى كلمات
    query_words = query.split()
    
    for word in query_words:
        if len(word) >= 2:  # تجاهل الكلمات القصيرة جداً
            pattern = re.compile(re.escape(word), re.IGNORECASE)
            text = pattern.sub(f'<mark>{word}</mark>', text)
    
    return text
