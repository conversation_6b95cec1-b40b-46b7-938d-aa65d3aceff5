from rest_framework import serializers
from .models import FeedType, FeedMixture, MixtureComponent
from ingredients.serializers import IngredientListSerializer


class FeedTypeSerializer(serializers.ModelSerializer):
    """مسلسل أنواع الأعلاف"""
    
    mixtures_count = serializers.SerializerMethodField()
    
    class Meta:
        model = FeedType
        fields = [
            'id', 'name', 'target_animal', 'description',
            'mixtures_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_mixtures_count(self, obj):
        """عدد الخلطات لهذا النوع"""
        return obj.mixtures.filter(is_active=True).count()


class MixtureComponentSerializer(serializers.ModelSerializer):
    """مسلسل مكونات الخلطة"""
    
    ingredient = IngredientListSerializer(read_only=True)
    ingredient_id = serializers.IntegerField(write_only=True)
    cost_contribution = serializers.DecimalField(max_digits=10, decimal_places=3, read_only=True)
    weight_in_batch = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = MixtureComponent
        fields = [
            'id', 'ingredient', 'ingredient_id', 'percentage',
            'cost_contribution', 'weight_in_batch', 'notes'
        ]


class FeedMixtureListSerializer(serializers.ModelSerializer):
    """مسلسل قائمة الخلطات (مبسط)"""
    
    feed_type_name = serializers.CharField(source='feed_type.name', read_only=True)
    is_formula_complete = serializers.BooleanField(read_only=True)
    total_components_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = FeedMixture
        fields = [
            'id', 'name', 'feed_type', 'feed_type_name',
            'cost_per_kg', 'selling_price_per_kg', 'profit_margin_percentage',
            'batch_size_kg', 'is_formula_complete', 'total_components_percentage',
            'is_active'
        ]


class FeedMixtureDetailSerializer(serializers.ModelSerializer):
    """مسلسل تفاصيل الخلطات (كامل)"""
    
    feed_type = FeedTypeSerializer(read_only=True)
    feed_type_id = serializers.IntegerField(write_only=True)
    components = MixtureComponentSerializer(many=True, read_only=True)
    nutritional_values = serializers.SerializerMethodField()
    is_formula_complete = serializers.BooleanField(read_only=True)
    total_components_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = FeedMixture
        fields = [
            'id', 'name', 'feed_type', 'feed_type_id', 'description',
            'target_protein_percentage', 'target_energy_kcal_per_kg',
            'target_fiber_percentage', 'cost_per_kg', 'selling_price_per_kg',
            'profit_margin_percentage', 'batch_size_kg', 'components',
            'nutritional_values', 'is_formula_complete',
            'total_components_percentage', 'notes', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'cost_per_kg', 'profit_margin_percentage']
    
    def get_nutritional_values(self, obj):
        """القيم الغذائية المحسوبة"""
        return obj.calculate_nutritional_values()


class FeedMixtureCreateUpdateSerializer(serializers.ModelSerializer):
    """مسلسل إنشاء وتحديث الخلطات"""
    
    components = MixtureComponentSerializer(many=True, required=False)
    
    class Meta:
        model = FeedMixture
        fields = [
            'name', 'feed_type', 'description', 'target_protein_percentage',
            'target_energy_kcal_per_kg', 'target_fiber_percentage',
            'selling_price_per_kg', 'batch_size_kg', 'components',
            'notes', 'is_active'
        ]
    
    def create(self, validated_data):
        components_data = validated_data.pop('components', [])
        mixture = FeedMixture.objects.create(**validated_data)
        
        for component_data in components_data:
            MixtureComponent.objects.create(mixture=mixture, **component_data)
        
        # حساب التكلفة
        mixture.calculate_cost()
        return mixture
    
    def update(self, instance, validated_data):
        components_data = validated_data.pop('components', None)
        
        # تحديث الخلطة
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # تحديث المكونات إذا تم تمريرها
        if components_data is not None:
            # حذف المكونات الحالية
            instance.components.all().delete()
            
            # إضافة المكونات الجديدة
            for component_data in components_data:
                MixtureComponent.objects.create(mixture=instance, **component_data)
        
        # إعادة حساب التكلفة
        instance.calculate_cost()
        return instance
    
    def validate_components(self, components):
        """التحقق من صحة المكونات"""
        if not components:
            return components
        
        total_percentage = sum(component.get('percentage', 0) for component in components)
        
        if total_percentage > 100:
            raise serializers.ValidationError(
                f'مجموع نسب المكونات ({total_percentage}%) يتجاوز 100%'
            )
        
        # التحقق من عدم تكرار المكونات
        ingredient_ids = [component.get('ingredient_id') for component in components]
        if len(ingredient_ids) != len(set(ingredient_ids)):
            raise serializers.ValidationError('لا يمكن تكرار نفس المكون في الخلطة')
        
        return components


class MixtureComponentCreateUpdateSerializer(serializers.ModelSerializer):
    """مسلسل إنشاء وتحديث مكونات الخلطة"""
    
    class Meta:
        model = MixtureComponent
        fields = ['ingredient', 'percentage', 'notes']
    
    def validate(self, data):
        """التحقق من صحة البيانات"""
        mixture = self.context.get('mixture')
        ingredient = data.get('ingredient')
        percentage = data.get('percentage', 0)
        
        if mixture and ingredient:
            # التحقق من عدم تكرار المكون في نفس الخلطة
            existing_component = MixtureComponent.objects.filter(
                mixture=mixture,
                ingredient=ingredient
            ).exclude(pk=self.instance.pk if self.instance else None)
            
            if existing_component.exists():
                raise serializers.ValidationError({
                    'ingredient': 'هذا المكون موجود بالفعل في الخلطة'
                })
            
            # التحقق من أن إجمالي النسب لا يتجاوز 100%
            current_total = mixture.total_components_percentage
            if self.instance:
                current_total -= self.instance.percentage
            
            if current_total + percentage > 100:
                raise serializers.ValidationError({
                    'percentage': f'إضافة هذه النسبة ستجعل المجموع يتجاوز 100% (المجموع الحالي: {current_total}%)'
                })
        
        return data
