from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils.translation import gettext_lazy as _
from orders.models import Order, Customer
from decimal import Decimal


class Invoice(models.Model):
    """نموذج الفاتورة"""

    INVOICE_STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('sent', _('مرسلة')),
        ('paid', _('مدفوعة')),
        ('overdue', _('متأخرة')),
        ('cancelled', _('ملغية')),
    ]

    # معلومات أساسية
    invoice_number = models.CharField(
        _('رقم الفاتورة'),
        max_length=20,
        unique=True,
        editable=False
    )
    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='invoice',
        verbose_name=_('الطلب')
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name=_('العميل')
    )

    # تواريخ
    issue_date = models.DateField(_('تاريخ الإصدار'), auto_now_add=True)
    due_date = models.DateField(_('تاريخ الاستحقاق'))

    # المبالغ المالية
    subtotal = models.DecimalField(
        _('المجموع الفرعي (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    tax_rate = models.DecimalField(
        _('معدل الضريبة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    tax_amount = models.DecimalField(
        _('مبلغ الضريبة (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    discount_rate = models.DecimalField(
        _('معدل الخصم %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    discount_amount = models.DecimalField(
        _('مبلغ الخصم (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    total_amount = models.DecimalField(
        _('المبلغ الإجمالي (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    paid_amount = models.DecimalField(
        _('المبلغ المدفوع (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )

    # حالة الفاتورة
    status = models.CharField(
        _('حالة الفاتورة'),
        max_length=20,
        choices=INVOICE_STATUS_CHOICES,
        default='draft'
    )

    # ملاحظات
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    terms_conditions = models.TextField(_('الشروط والأحكام'), blank=True, null=True)

    # معلومات النظام
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_invoices',
        verbose_name=_('أنشئت بواسطة')
    )
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فاتورة')
        verbose_name_plural = _('الفواتير')
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # نسخ البيانات من الطلب
        if self.order:
            self.customer = self.order.customer
            self.subtotal = self.order.subtotal
            self.tax_rate = self.order.tax_percentage
            self.tax_amount = self.order.tax_amount
            self.discount_rate = self.order.discount_percentage
            self.discount_amount = self.order.discount_amount
            self.total_amount = self.order.total_amount

        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """توليد رقم فاتورة فريد"""
        from datetime import datetime
        year = datetime.now().year
        month = datetime.now().month

        # البحث عن آخر فاتورة في نفس الشهر
        last_invoice = Invoice.objects.filter(
            created_at__year=year,
            created_at__month=month
        ).order_by('-id').first()

        if last_invoice:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"INV-{year}{month:02d}-{new_number:04d}"

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    @property
    def is_paid(self):
        """هل الفاتورة مدفوعة بالكامل"""
        return self.paid_amount >= self.total_amount

    @property
    def is_overdue(self):
        """هل الفاتورة متأخرة"""
        from datetime import date
        return self.due_date < date.today() and not self.is_paid


class InvoiceItem(models.Model):
    """عناصر الفاتورة"""

    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('الفاتورة')
    )
    description = models.CharField(_('الوصف'), max_length=200)
    quantity = models.DecimalField(
        _('الكمية'),
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(0)]
    )
    unit_price = models.DecimalField(
        _('سعر الوحدة (دينار أردني)'),
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(0)]
    )
    total_price = models.DecimalField(
        _('السعر الإجمالي (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        editable=False
    )

    class Meta:
        verbose_name = _('عنصر فاتورة')
        verbose_name_plural = _('عناصر الفاتورة')

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.description} - {self.quantity} × {self.unit_price}"


class Payment(models.Model):
    """نموذج الدفعات"""

    PAYMENT_METHOD_CHOICES = [
        ('cash', _('نقداً')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
        ('other', _('أخرى')),
    ]

    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('الفاتورة')
    )
    amount = models.DecimalField(
        _('المبلغ (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)]
    )
    payment_date = models.DateField(_('تاريخ الدفع'))
    payment_method = models.CharField(
        _('طريقة الدفع'),
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        default='cash'
    )
    reference_number = models.CharField(
        _('رقم المرجع'),
        max_length=100,
        blank=True,
        null=True
    )
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    # معلومات النظام
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('أنشئت بواسطة')
    )
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('الدفعات')
        ordering = ['-payment_date']

    def __str__(self):
        return f"دفعة {self.amount} د.أ - {self.invoice.invoice_number}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # تحديث المبلغ المدفوع في الفاتورة
        self.invoice.paid_amount = self.invoice.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or 0

        # تحديث حالة الفاتورة
        if self.invoice.is_paid:
            self.invoice.status = 'paid'
        elif self.invoice.paid_amount > 0:
            self.invoice.status = 'sent'

        self.invoice.save()
