"""
نظام البحث المتقدم
"""
from django.db.models import Q, Count, Sum, Avg
from django.contrib.contenttypes.models import ContentType
from django.apps import apps
import re

class AdvancedSearchEngine:
    """محرك البحث المتقدم"""
    
    def __init__(self):
        self.searchable_models = {
            'ingredients': {
                'model': 'ingredients.Ingredient',
                'fields': ['name', 'description', 'supplier'],
                'display_name': 'المكونات'
            },
            'mixtures': {
                'model': 'mixtures.FeedMixture',
                'fields': ['name', 'description'],
                'display_name': 'الخلطات'
            },
            'customers': {
                'model': 'orders.Customer',
                'fields': ['name', 'email', 'phone', 'address'],
                'display_name': 'العملاء'
            },
            'orders': {
                'model': 'orders.Order',
                'fields': ['order_number', 'notes'],
                'display_name': 'الطلبات'
            },
            'invoices': {
                'model': 'invoices.Invoice',
                'fields': ['invoice_number', 'notes'],
                'display_name': 'الفواتير'
            }
        }
    
    def search_all(self, query, limit=50):
        """البحث في جميع النماذج"""
        results = []
        
        if not query or len(query.strip()) < 2:
            return results
        
        query = query.strip()
        
        for model_key, config in self.searchable_models.items():
            model_results = self.search_model(model_key, query, limit=10)
            results.extend(model_results)
        
        # ترتيب النتائج حسب الصلة
        results.sort(key=lambda x: x['relevance'], reverse=True)
        
        return results[:limit]
    
    def search_model(self, model_key, query, limit=10):
        """البحث في نموذج محدد"""
        if model_key not in self.searchable_models:
            return []
        
        config = self.searchable_models[model_key]
        
        try:
            # الحصول على النموذج
            app_label, model_name = config['model'].split('.')
            model = apps.get_model(app_label, model_name)
            
            # بناء استعلام البحث
            search_query = Q()
            for field in config['fields']:
                search_query |= Q(**{f"{field}__icontains": query})
            
            # تنفيذ البحث
            objects = model.objects.filter(search_query)
            
            # إضافة فلاتر إضافية حسب النموذج
            if hasattr(model, 'is_active'):
                objects = objects.filter(is_active=True)
            
            objects = objects[:limit]
            
            # تحويل النتائج
            results = []
            for obj in objects:
                result = self.format_result(obj, config, query)
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"خطأ في البحث في {model_key}: {e}")
            return []
    
    def format_result(self, obj, config, query):
        """تنسيق نتيجة البحث"""
        # حساب درجة الصلة
        relevance = self.calculate_relevance(obj, config['fields'], query)
        
        # الحصول على الرابط
        url = self.get_object_url(obj)
        
        # الحصول على الوصف
        description = self.get_object_description(obj, config['fields'])
        
        # تمييز النص المطابق
        highlighted_description = self.highlight_matches(description, query)
        
        return {
            'id': obj.pk,
            'title': str(obj),
            'description': highlighted_description,
            'url': url,
            'model_name': config['display_name'],
            'relevance': relevance,
            'icon': self.get_model_icon(config['model'])
        }
    
    def calculate_relevance(self, obj, fields, query):
        """حساب درجة صلة النتيجة"""
        relevance = 0
        query_lower = query.lower()
        
        for field in fields:
            try:
                field_value = str(getattr(obj, field, '')).lower()
                
                # مطابقة تامة
                if query_lower == field_value:
                    relevance += 100
                # يبدأ بالاستعلام
                elif field_value.startswith(query_lower):
                    relevance += 50
                # يحتوي على الاستعلام
                elif query_lower in field_value:
                    relevance += 25
                # مطابقة جزئية
                else:
                    words = query_lower.split()
                    for word in words:
                        if word in field_value:
                            relevance += 10
            except:
                continue
        
        return relevance
    
    def get_object_url(self, obj):
        """الحصول على رابط الكائن"""
        model_name = obj._meta.model_name
        app_label = obj._meta.app_label
        
        # محاولة بناء الرابط
        try:
            if app_label == 'ingredients':
                return f'/ingredients/{obj.pk}/'
            elif app_label == 'mixtures':
                return f'/mixtures/{obj.pk}/'
            elif app_label == 'orders' and model_name == 'customer':
                return f'/orders/customers/{obj.pk}/'
            elif app_label == 'orders' and model_name == 'order':
                return f'/orders/{obj.pk}/'
            elif app_label == 'invoices':
                return f'/invoices/{obj.pk}/'
            else:
                return '#'
        except:
            return '#'
    
    def get_object_description(self, obj, fields):
        """الحصول على وصف الكائن"""
        description_parts = []
        
        for field in fields[:3]:  # أول 3 حقول فقط
            try:
                value = getattr(obj, field, '')
                if value:
                    description_parts.append(str(value)[:100])
            except:
                continue
        
        return ' | '.join(description_parts)
    
    def highlight_matches(self, text, query):
        """تمييز النص المطابق"""
        if not text or not query:
            return text
        
        # تمييز المطابقات
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        highlighted = pattern.sub(f'<mark>\\g<0></mark>', text)
        
        return highlighted
    
    def get_model_icon(self, model_path):
        """الحصول على أيقونة النموذج"""
        icons = {
            'ingredients.Ingredient': 'fas fa-seedling',
            'mixtures.FeedMixture': 'fas fa-blender',
            'orders.Customer': 'fas fa-user',
            'orders.Order': 'fas fa-shopping-cart',
            'invoices.Invoice': 'fas fa-file-invoice'
        }
        
        return icons.get(model_path, 'fas fa-search')
    
    def get_search_suggestions(self, query, limit=10):
        """الحصول على اقتراحات البحث"""
        if not query or len(query.strip()) < 2:
            return []
        
        suggestions = []
        
        # اقتراحات من أسماء المكونات
        try:
            from ingredients.models import Ingredient
            ingredients = Ingredient.objects.filter(
                name__icontains=query,
                is_active=True
            ).values_list('name', flat=True)[:5]
            
            for name in ingredients:
                suggestions.append({
                    'text': name,
                    'type': 'مكون',
                    'icon': 'fas fa-seedling'
                })
        except:
            pass
        
        # اقتراحات من أسماء العملاء
        try:
            from orders.models import Customer
            customers = Customer.objects.filter(
                name__icontains=query,
                is_active=True
            ).values_list('name', flat=True)[:5]
            
            for name in customers:
                suggestions.append({
                    'text': name,
                    'type': 'عميل',
                    'icon': 'fas fa-user'
                })
        except:
            pass
        
        return suggestions[:limit]

# إنشاء مثيل عام
search_engine = AdvancedSearchEngine()
