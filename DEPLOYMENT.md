# 🚀 دليل النشر - نظام إدارة مصنع الأعلاف

## 📋 متطلبات النشر

### الخادم
- Ubuntu 20.04+ أو CentOS 8+
- RAM: 2GB كحد أدنى (4GB مُوصى به)
- مساحة التخزين: 20GB كحد أدنى
- Python 3.8+
- PostgreSQL 12+
- Nginx
- SSL Certificate (اختياري)

## 🐳 النشر باستخدام Docker (الطريقة المُوصى بها)

### 1. تثبيت Docker و Docker Compose

```bash
# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. إعد<PERSON> المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd feed_factory

# إنشاء ملف متغيرات البيئة
cp .env.example .env
nano .env
```

### 3. تحرير متغيرات البيئة

```bash
# .env
DEBUG=0
SECRET_KEY=your-very-secret-key-here
DB_NAME=feed_factory_db
DB_USER=feed_factory_user
DB_PASSWORD=your-strong-password
DB_HOST=db
DB_PORT=5432

# إعدادات البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# النطاق
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

### 4. تشغيل النظام

```bash
# بناء وتشغيل الحاويات
docker-compose up -d --build

# التحقق من حالة الحاويات
docker-compose ps

# عرض السجلات
docker-compose logs -f
```

### 5. إعداد SSL (اختياري)

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# تجديد تلقائي للشهادة
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🖥️ النشر التقليدي (بدون Docker)

### 1. إعداد الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت المتطلبات
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib nginx git -y
```

### 2. إعداد قاعدة البيانات

```bash
# تسجيل الدخول إلى PostgreSQL
sudo -u postgres psql

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE feed_factory_db;
CREATE USER feed_factory_user WITH PASSWORD 'your_password';
ALTER ROLE feed_factory_user SET client_encoding TO 'utf8';
ALTER ROLE feed_factory_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE feed_factory_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE feed_factory_db TO feed_factory_user;
\q
```

### 3. إعداد المشروع

```bash
# إنشاء مستخدم للتطبيق
sudo adduser feedfactory
sudo usermod -aG sudo feedfactory
su - feedfactory

# استنساخ المشروع
git clone <repository-url>
cd feed_factory

# إنشاء البيئة الافتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# إعداد متغيرات البيئة
cp .env.example .env
nano .env
```

### 4. إعداد Django

```bash
# تطبيق الهجرات
python manage.py migrate

# جمع الملفات الثابتة
python manage.py collectstatic

# إنشاء مستخدم إداري
python manage.py createsuperuser

# إضافة البيانات التجريبية (اختياري)
python add_sample_data.py
```

### 5. إعداد Gunicorn

```bash
# تثبيت Gunicorn
pip install gunicorn

# إنشاء ملف خدمة systemd
sudo nano /etc/systemd/system/feedfactory.service
```

```ini
[Unit]
Description=Feed Factory Django App
After=network.target

[Service]
User=feedfactory
Group=www-data
WorkingDirectory=/home/<USER>/feed_factory
Environment="PATH=/home/<USER>/feed_factory/venv/bin"
ExecStart=/home/<USER>/feed_factory/venv/bin/gunicorn --access-logfile - --workers 3 --bind unix:/home/<USER>/feed_factory/feed_factory.sock feed_factory.wsgi:application

[Install]
WantedBy=multi-user.target
```

```bash
# تفعيل وتشغيل الخدمة
sudo systemctl start feedfactory
sudo systemctl enable feedfactory
sudo systemctl status feedfactory
```

### 6. إعداد Nginx

```bash
# إنشاء ملف إعداد Nginx
sudo nano /etc/nginx/sites-available/feedfactory
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    location /static/ {
        root /home/<USER>/feed_factory;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        root /home/<USER>/feed_factory;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/home/<USER>/feed_factory/feed_factory.sock;
    }
}
```

```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/feedfactory /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx
```

## 🔧 الصيانة والمراقبة

### النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
pg_dump -U feed_factory_user -h localhost feed_factory_db > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخ احتياطي للملفات
tar -czf media_backup_$(date +%Y%m%d_%H%M%S).tar.gz media/
```

### المراقبة

```bash
# مراقبة السجلات
sudo journalctl -u feedfactory -f

# مراقبة استخدام الموارد
htop
df -h
free -h
```

### التحديث

```bash
# سحب آخر التحديثات
git pull origin main

# تطبيق الهجرات الجديدة
python manage.py migrate

# جمع الملفات الثابتة
python manage.py collectstatic --noinput

# إعادة تشغيل الخدمة
sudo systemctl restart feedfactory
```

## 🔒 الأمان

### إعدادات الأمان الأساسية

1. **تغيير كلمات المرور الافتراضية**
2. **تفعيل الجدار الناري**
3. **تحديث النظام بانتظام**
4. **استخدام شهادات SSL**
5. **إعداد النسخ الاحتياطي التلقائي**

### مراقبة الأمان

```bash
# مراقبة محاولات تسجيل الدخول
sudo tail -f /var/log/auth.log

# مراقبة سجلات Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 📞 الدعم

في حالة مواجهة مشاكل في النشر:
1. تحقق من السجلات
2. تأكد من إعدادات قاعدة البيانات
3. تحقق من صلاحيات الملفات
4. راجع إعدادات الجدار الناري

---

**ملاحظة**: تأكد من تغيير جميع كلمات المرور والمفاتيح الافتراضية قبل النشر في الإنتاج.
