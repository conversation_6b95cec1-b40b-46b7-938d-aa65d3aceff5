#!/usr/bin/env python
import os
import sys
import subprocess

def main():
    print("🚀 Starting Django Development Server")
    print("=" * 50)
    
    # التأكد من أننا في المجلد الصحيح
    if not os.path.exists('manage.py'):
        print("❌ manage.py not found!")
        return
    
    # التحقق من Django
    try:
        import django
        print(f"✅ Django {django.get_version()} is available")
    except ImportError:
        print("❌ Django is not installed!")
        return
    
    # تشغيل الخادم
    print("🌐 Server will be available at: http://127.0.0.1:8000/")
    print("🔑 Admin panel: http://127.0.0.1:8000/admin/")
    print("👤 Username: admin | 🔐 Password: admin123456")
    print("-" * 50)
    
    try:
        # تشغيل الخادم باستخدام subprocess
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
