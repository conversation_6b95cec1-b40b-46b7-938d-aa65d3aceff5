from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from ingredients.models import Ingredient


class FeedType(models.Model):
    """أنواع الأعلاف مثل علف دجاج، علف أبقار، علف أغنام"""
    name = models.CharField(_('نوع العلف'), max_length=100, unique=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    target_animal = models.CharField(_('الحيوان المستهدف'), max_length=100)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نوع العلف')
        verbose_name_plural = _('أنواع الأعلاف')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.target_animal}"


class FeedMixture(models.Model):
    """نموذج خلطات الأعلاف"""
    name = models.CharField(_('اسم الخلطة'), max_length=200, unique=True)
    feed_type = models.ForeignKey(
        FeedType,
        on_delete=models.CASCADE,
        verbose_name=_('نوع العلف'),
        related_name='mixtures'
    )
    description = models.TextField(_('الوصف'), blank=True, null=True)

    # المتطلبات الغذائية المستهدفة
    target_protein_percentage = models.DecimalField(
        _('نسبة البروتين المستهدفة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )
    target_energy_kcal_per_kg = models.DecimalField(
        _('الطاقة المستهدفة (كيلو كالوري/كغم)'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    target_fiber_percentage = models.DecimalField(
        _('نسبة الألياف المستهدفة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0
    )

    # معلومات التكلفة والسعر
    cost_per_kg = models.DecimalField(
        _('تكلفة الإنتاج لكل كغم (دينار أردني)'),
        max_digits=10,
        decimal_places=3,
        default=0,
        editable=False  # يتم حسابها تلقائياً
    )
    selling_price_per_kg = models.DecimalField(
        _('سعر البيع لكل كغم (دينار أردني)'),
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    profit_margin_percentage = models.DecimalField(
        _('هامش الربح %'),
        max_digits=5,
        decimal_places=2,
        default=0,
        editable=False  # يتم حسابها تلقائياً
    )

    # معلومات إضافية
    batch_size_kg = models.DecimalField(
        _('حجم الدفعة (كغم)'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=1000
    )
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('خلطة علف')
        verbose_name_plural = _('خلطات الأعلاف')
        ordering = ['name']

    def __str__(self):
        return self.name

    def calculate_nutritional_values(self):
        """حساب القيم الغذائية للخلطة"""
        total_protein = 0
        total_energy = 0
        total_fiber = 0
        total_percentage = 0

        for component in self.components.all():
            percentage = component.percentage / 100
            total_protein += component.ingredient.protein_percentage * percentage
            total_energy += component.ingredient.energy_kcal_per_kg * percentage
            total_fiber += component.ingredient.fiber_percentage * percentage
            total_percentage += component.percentage

        return {
            'protein_percentage': round(total_protein, 2),
            'energy_kcal_per_kg': round(total_energy, 2),
            'fiber_percentage': round(total_fiber, 2),
            'total_percentage': round(total_percentage, 2)
        }

    def calculate_cost(self):
        """حساب تكلفة الخلطة"""
        total_cost = 0
        for component in self.components.all():
            percentage = component.percentage / 100
            component_cost = component.ingredient.cost_per_kg * percentage
            total_cost += component_cost

        self.cost_per_kg = round(total_cost, 3)

        # حساب هامش الربح
        if self.selling_price_per_kg > 0:
            profit = self.selling_price_per_kg - self.cost_per_kg
            self.profit_margin_percentage = round((profit / self.selling_price_per_kg) * 100, 2)
        else:
            self.profit_margin_percentage = 0

        self.save()
        return self.cost_per_kg

    @property
    def total_components_percentage(self):
        """إجمالي نسب المكونات"""
        return sum(component.percentage for component in self.components.all())

    @property
    def is_formula_complete(self):
        """تحقق من اكتمال الصيغة (مجموع النسب = 100%)"""
        return abs(self.total_components_percentage - 100) < 0.01

    @property
    def profit_per_kg(self):
        """حساب الربح لكل كيلوغرام"""
        if self.cost_per_kg and self.selling_price_per_kg:
            return self.selling_price_per_kg - self.cost_per_kg
        return 0


class MixtureComponent(models.Model):
    """مكونات الخلطة مع النسب"""
    mixture = models.ForeignKey(
        FeedMixture,
        on_delete=models.CASCADE,
        verbose_name=_('الخلطة'),
        related_name='components'
    )
    ingredient = models.ForeignKey(
        Ingredient,
        on_delete=models.CASCADE,
        verbose_name=_('المكون')
    )
    percentage = models.DecimalField(
        _('النسبة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مكون الخلطة')
        verbose_name_plural = _('مكونات الخلطة')
        unique_together = ['mixture', 'ingredient']
        ordering = ['-percentage']

    def __str__(self):
        return f"{self.mixture.name} - {self.ingredient.name} ({self.percentage}%)"

    @property
    def cost_contribution(self):
        """مساهمة هذا المكون في التكلفة الإجمالية"""
        return (self.percentage / 100) * self.ingredient.cost_per_kg

    @property
    def weight_in_batch(self):
        """وزن هذا المكون في الدفعة"""
        return (self.percentage / 100) * self.mixture.batch_size_kg
