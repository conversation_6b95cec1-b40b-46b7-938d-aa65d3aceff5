{% extends 'base.html' %}

{% block title %}فاتورة {{ invoice.invoice_number }} - مصنع الأعلاف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{% url 'invoices:list' %}">الفواتير</a></li>
        <li class="breadcrumb-item active">فاتورة {{ invoice.invoice_number }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-file-invoice text-primary"></i>
                فاتورة {{ invoice.invoice_number }}
            </h1>
            <div>
                <a href="{% url 'invoices:pdf' invoice.pk %}" class="btn btn-danger">
                    <i class="fas fa-file-pdf"></i>
                    تحميل PDF
                </a>
                {% if not invoice.is_paid %}
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#paymentModal">
                    <i class="fas fa-plus"></i>
                    إضافة دفعة
                </button>
                {% endif %}
                <a href="{% url 'invoices:list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الفاتورة -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">تفاصيل الفاتورة</h5>
            </div>
            <div class="card-body">
                <!-- معلومات الشركة والعميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary">من:</h6>
                        <strong>مصنع الأعلاف</strong><br>
                        عمان، الأردن<br>
                        هاتف: +962 6 123 4567<br>
                        البريد: <EMAIL>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">إلى:</h6>
                        <strong>{{ invoice.customer.name }}</strong><br>
                        {% if invoice.customer.company_name %}{{ invoice.customer.company_name }}<br>{% endif %}
                        {% if invoice.customer.address %}{{ invoice.customer.address }}<br>{% endif %}
                        {% if invoice.customer.city %}{{ invoice.customer.city }}<br>{% endif %}
                        هاتف: {{ invoice.customer.phone }}
                    </div>
                </div>

                <!-- معلومات التواريخ -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <strong>تاريخ الإصدار:</strong><br>
                        {{ invoice.issue_date|date:"d/m/Y" }}
                    </div>
                    <div class="col-md-4">
                        <strong>تاريخ الاستحقاق:</strong><br>
                        {{ invoice.due_date|date:"d/m/Y" }}
                        {% if invoice.is_overdue %}
                            <span class="badge bg-danger ms-1">متأخرة</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <strong>رقم الطلب:</strong><br>
                        <a href="{% url 'orders:detail' invoice.order.pk %}">{{ invoice.order.order_number }}</a>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>الوصف</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice.items.all %}
                            <tr>
                                <td>{{ item.description }}</td>
                                <td>{{ item.quantity|floatformat:2 }} كغم</td>
                                <td class="currency">{{ item.unit_price|floatformat:3 }} د.أ</td>
                                <td class="currency">{{ item.total_price|floatformat:2 }} د.أ</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="3">المجموع الفرعي:</th>
                                <th class="currency">{{ invoice.subtotal|floatformat:2 }} د.أ</th>
                            </tr>
                            {% if invoice.discount_amount > 0 %}
                            <tr>
                                <th colspan="3">الخصم ({{ invoice.discount_rate }}%):</th>
                                <th class="currency text-danger">-{{ invoice.discount_amount|floatformat:2 }} د.أ</th>
                            </tr>
                            {% endif %}
                            {% if invoice.tax_amount > 0 %}
                            <tr>
                                <th colspan="3">الضريبة ({{ invoice.tax_rate }}%):</th>
                                <th class="currency">{{ invoice.tax_amount|floatformat:2 }} د.أ</th>
                            </tr>
                            {% endif %}
                            <tr class="table-primary">
                                <th colspan="3">المجموع الإجمالي:</th>
                                <th class="currency">{{ invoice.total_amount|floatformat:2 }} د.أ</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                {% if invoice.notes %}
                <div class="mt-4">
                    <h6>ملاحظات:</h6>
                    <p>{{ invoice.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- حالة الفاتورة -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0">حالة الفاتورة</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if invoice.status == 'draft' %}
                        <span class="badge bg-secondary fs-6">مسودة</span>
                    {% elif invoice.status == 'sent' %}
                        <span class="badge bg-primary fs-6">مرسلة</span>
                    {% elif invoice.status == 'paid' %}
                        <span class="badge bg-success fs-6">مدفوعة</span>
                    {% elif invoice.status == 'overdue' %}
                        <span class="badge bg-danger fs-6">متأخرة</span>
                    {% elif invoice.status == 'cancelled' %}
                        <span class="badge bg-dark fs-6">ملغية</span>
                    {% endif %}
                </div>
                
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>المبلغ الإجمالي:</span>
                        <strong class="currency">{{ invoice.total_amount|floatformat:2 }} د.أ</strong>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>المبلغ المدفوع:</span>
                        <strong class="currency text-success">{{ invoice.paid_amount|floatformat:2 }} د.أ</strong>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>المبلغ المتبقي:</span>
                        <strong class="currency text-danger">{{ invoice.remaining_amount|floatformat:2 }} د.أ</strong>
                    </div>
                </div>

                <!-- شريط التقدم -->
                {% if invoice.total_amount > 0 %}
                <div class="progress mb-3">
                    {% widthratio invoice.paid_amount invoice.total_amount 100 as progress_percent %}
                    <div class="progress-bar bg-success" style="width: {{ progress_percent }}%"></div>
                </div>
                <small class="text-muted">{{ progress_percent }}% مدفوع</small>
                {% endif %}
            </div>
        </div>

        <!-- سجل الدفعات -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">سجل الدفعات</h6>
            </div>
            <div class="card-body">
                {% if payments %}
                    {% for payment in payments %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <div>
                            <strong class="currency">{{ payment.amount|floatformat:2 }} د.أ</strong><br>
                            <small class="text-muted">{{ payment.payment_date|date:"d/m/Y" }} - {{ payment.get_payment_method_display }}</small>
                        </div>
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-credit-card fa-2x mb-2"></i>
                        <p>لا توجد دفعات بعد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة دفعة -->
{% if not invoice.is_paid %}
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'invoices:add_payment' invoice.pk %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">المبلغ (د.أ)</label>
                        <input type="number" name="amount" class="form-control" step="0.001" max="{{ invoice.remaining_amount }}" required>
                        <small class="text-muted">الحد الأقصى: {{ invoice.remaining_amount|floatformat:2 }} د.أ</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ الدفع</label>
                        <input type="date" name="payment_date" class="form-control" value="{% now 'Y-m-d' %}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select name="payment_method" class="form-select" required>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم المرجع</label>
                        <input type="text" name="reference_number" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة الدفعة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
