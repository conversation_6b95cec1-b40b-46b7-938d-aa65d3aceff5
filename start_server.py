#!/usr/bin/env python
"""
تشغيل خادم Django
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
    
    try:
        django.setup()
        print("✅ Django setup successful")
        print("🚀 Starting Django development server...")
        print("🌐 Server will be available at: http://127.0.0.1:8000/")
        print("🔑 Admin panel: http://127.0.0.1:8000/admin/")
        print("👤 Username: admin")
        print("🔐 Password: admin123456")
        print("-" * 50)
        
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000'])
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
