from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils.translation import gettext_lazy as _
from mixtures.models import FeedMixture


class Customer(models.Model):
    """نموذج العملاء"""
    name = models.CharField(_('اسم العميل'), max_length=200)
    company_name = models.CharField(_('اسم الشركة'), max_length=200, blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    city = models.CharField(_('المدينة'), max_length=100, blank=True, null=True)

    # معلومات مالية
    credit_limit = models.DecimalField(
        _('حد الائتمان (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0)],
        default=0
    )
    current_balance = models.DecimalField(
        _('الرصيد الحالي (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        default=0
    )

    # معلومات إضافية
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def available_credit(self):
        """الائتمان المتاح"""
        return self.credit_limit + self.current_balance


class Order(models.Model):
    """نموذج الطلبات"""

    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('confirmed', _('مؤكد')),
        ('in_production', _('قيد الإنتاج')),
        ('ready', _('جاهز')),
        ('delivered', _('تم التسليم')),
        ('cancelled', _('ملغي')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('unpaid', _('غير مدفوع')),
        ('partial', _('مدفوع جزئياً')),
        ('paid', _('مدفوع بالكامل')),
    ]

    order_number = models.CharField(_('رقم الطلب'), max_length=20, unique=True)
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        verbose_name=_('العميل'),
        related_name='orders'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('أنشئ بواسطة'),
        related_name='created_orders'
    )

    # تواريخ مهمة
    order_date = models.DateTimeField(_('تاريخ الطلب'), auto_now_add=True)
    required_date = models.DateField(_('التاريخ المطلوب'))
    delivery_date = models.DateTimeField(_('تاريخ التسليم'), blank=True, null=True)

    # حالة الطلب
    status = models.CharField(
        _('حالة الطلب'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    payment_status = models.CharField(
        _('حالة الدفع'),
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='unpaid'
    )

    # المبالغ المالية
    subtotal = models.DecimalField(
        _('المجموع الفرعي (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )
    discount_percentage = models.DecimalField(
        _('نسبة الخصم %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    discount_amount = models.DecimalField(
        _('مبلغ الخصم (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )
    tax_percentage = models.DecimalField(
        _('نسبة الضريبة %'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0
    )
    tax_amount = models.DecimalField(
        _('مبلغ الضريبة (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )
    total_amount = models.DecimalField(
        _('المبلغ الإجمالي (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )
    paid_amount = models.DecimalField(
        _('المبلغ المدفوع (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0
    )

    # معلومات إضافية
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    delivery_address = models.TextField(_('عنوان التسليم'), blank=True, null=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('طلب')
        verbose_name_plural = _('الطلبات')
        ordering = ['-order_date']

    def __str__(self):
        return f"{self.order_number} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            # إنشاء رقم طلب تلقائي
            from django.utils import timezone
            today = timezone.now().date()
            count = Order.objects.filter(order_date__date=today).count() + 1
            self.order_number = f"ORD-{today.strftime('%Y%m%d')}-{count:04d}"
        super().save(*args, **kwargs)

    def calculate_totals(self):
        """حساب المجاميع المالية"""
        # حساب المجموع الفرعي
        self.subtotal = sum(item.total_price for item in self.items.all())

        # حساب الخصم
        self.discount_amount = (self.subtotal * self.discount_percentage) / 100

        # حساب المبلغ بعد الخصم
        amount_after_discount = self.subtotal - self.discount_amount

        # حساب الضريبة
        self.tax_amount = (amount_after_discount * self.tax_percentage) / 100

        # حساب المجموع الإجمالي
        self.total_amount = amount_after_discount + self.tax_amount

        # تحديث حالة الدفع
        if self.paid_amount >= self.total_amount:
            self.payment_status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        else:
            self.payment_status = 'unpaid'

        self.save()

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    @property
    def total_weight(self):
        """إجمالي الوزن"""
        return sum(item.quantity_kg for item in self.items.all())


class OrderItem(models.Model):
    """عناصر الطلب"""
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        verbose_name=_('الطلب'),
        related_name='items'
    )
    mixture = models.ForeignKey(
        FeedMixture,
        on_delete=models.CASCADE,
        verbose_name=_('خلطة العلف')
    )
    quantity_kg = models.DecimalField(
        _('الكمية (كغم)'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    unit_price = models.DecimalField(
        _('سعر الوحدة (دينار/كغم)'),
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(0)]
    )
    total_price = models.DecimalField(
        _('السعر الإجمالي (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عنصر الطلب')
        verbose_name_plural = _('عناصر الطلب')
        unique_together = ['order', 'mixture']

    def __str__(self):
        return f"{self.order.order_number} - {self.mixture.name}"

    def save(self, *args, **kwargs):
        # حساب السعر الإجمالي
        self.total_price = self.quantity_kg * self.unit_price
        super().save(*args, **kwargs)

        # إعادة حساب مجاميع الطلب
        self.order.calculate_totals()
