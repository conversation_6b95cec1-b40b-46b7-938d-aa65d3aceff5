#!/usr/bin/env python
"""
إضافة بيانات تجريبية لنظام مصنع الأعلاف
"""

import os
import sys
import django
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'feed_factory.settings')
django.setup()

from ingredients.models import IngredientCategory, Ingredient
from mixtures.models import FeedType, FeedMixture, MixtureComponent
from orders.models import Customer, Order, OrderItem
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, timedelta

def create_ingredient_categories():
    """إنشاء فئات المكونات"""
    categories = [
        {'name': 'الحبوب', 'description': 'الذرة، القمح، الشعير، الأرز'},
        {'name': 'البروتينات', 'description': 'كسب الصويا، كسب عباد الشمس، مسحوق السمك'},
        {'name': 'الفيتامينات والمعادن', 'description': 'الفيتامينات والأملاح المعدنية'},
        {'name': 'الدهون والزيوت', 'description': 'زيت الصويا، زيت النخيل'},
        {'name': 'الألياف', 'description': 'نخالة القمح، قشور الأرز'},
    ]
    
    created_categories = []
    for cat_data in categories:
        category, created = IngredientCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        created_categories.append(category)
        print(f"{'✓ تم إنشاء' if created else '✓ موجود'} فئة: {category.name}")
    
    return created_categories

def create_ingredients():
    """إنشاء المكونات"""
    categories = {cat.name: cat for cat in IngredientCategory.objects.all()}
    
    ingredients_data = [
        # الحبوب
        {
            'name': 'ذرة صفراء',
            'category': categories['الحبوب'],
            'protein_percentage': Decimal('8.5'),
            'energy_kcal_per_kg': Decimal('3350'),
            'fiber_percentage': Decimal('2.2'),
            'fat_percentage': Decimal('3.8'),
            'cost_per_kg': Decimal('0.450'),
            'current_stock': Decimal('5000'),
            'minimum_stock': Decimal('1000'),
            'supplier': 'شركة الحبوب المتحدة'
        },
        {
            'name': 'قمح',
            'category': categories['الحبوب'],
            'protein_percentage': Decimal('12.0'),
            'energy_kcal_per_kg': Decimal('3200'),
            'fiber_percentage': Decimal('2.8'),
            'fat_percentage': Decimal('1.8'),
            'cost_per_kg': Decimal('0.520'),
            'current_stock': Decimal('3000'),
            'minimum_stock': Decimal('800'),
            'supplier': 'مطاحن الشرق'
        },
        {
            'name': 'شعير',
            'category': categories['الحبوب'],
            'protein_percentage': Decimal('11.5'),
            'energy_kcal_per_kg': Decimal('2900'),
            'fiber_percentage': Decimal('5.5'),
            'fat_percentage': Decimal('2.1'),
            'cost_per_kg': Decimal('0.380'),
            'current_stock': Decimal('2500'),
            'minimum_stock': Decimal('500'),
            'supplier': 'شركة الحبوب المتحدة'
        },
        
        # البروتينات
        {
            'name': 'كسب الصويا',
            'category': categories['البروتينات'],
            'protein_percentage': Decimal('44.0'),
            'energy_kcal_per_kg': Decimal('2230'),
            'fiber_percentage': Decimal('7.0'),
            'fat_percentage': Decimal('1.5'),
            'cost_per_kg': Decimal('0.850'),
            'current_stock': Decimal('1500'),
            'minimum_stock': Decimal('300'),
            'supplier': 'شركة البروتينات العالمية'
        },
        {
            'name': 'مسحوق السمك',
            'category': categories['البروتينات'],
            'protein_percentage': Decimal('65.0'),
            'energy_kcal_per_kg': Decimal('2800'),
            'fiber_percentage': Decimal('1.0'),
            'fat_percentage': Decimal('8.0'),
            'cost_per_kg': Decimal('1.200'),
            'current_stock': Decimal('800'),
            'minimum_stock': Decimal('200'),
            'supplier': 'مصانع الأسماك المتطورة'
        },
        
        # الفيتامينات والمعادن
        {
            'name': 'خليط فيتامينات ومعادن',
            'category': categories['الفيتامينات والمعادن'],
            'protein_percentage': Decimal('0.0'),
            'energy_kcal_per_kg': Decimal('0'),
            'fiber_percentage': Decimal('0.0'),
            'fat_percentage': Decimal('0.0'),
            'cost_per_kg': Decimal('2.500'),
            'current_stock': Decimal('500'),
            'minimum_stock': Decimal('100'),
            'supplier': 'شركة التغذية المتخصصة'
        },
        
        # الدهون والزيوت
        {
            'name': 'زيت الصويا',
            'category': categories['الدهون والزيوت'],
            'protein_percentage': Decimal('0.0'),
            'energy_kcal_per_kg': Decimal('8800'),
            'fiber_percentage': Decimal('0.0'),
            'fat_percentage': Decimal('99.0'),
            'cost_per_kg': Decimal('0.950'),
            'current_stock': Decimal('1000'),
            'minimum_stock': Decimal('200'),
            'supplier': 'مصانع الزيوت النباتية'
        },
        
        # الألياف
        {
            'name': 'نخالة القمح',
            'category': categories['الألياف'],
            'protein_percentage': Decimal('15.5'),
            'energy_kcal_per_kg': Decimal('1200'),
            'fiber_percentage': Decimal('42.0'),
            'fat_percentage': Decimal('4.0'),
            'cost_per_kg': Decimal('0.280'),
            'current_stock': Decimal('2000'),
            'minimum_stock': Decimal('400'),
            'supplier': 'مطاحن الشرق'
        },
    ]
    
    created_ingredients = []
    for ing_data in ingredients_data:
        ingredient, created = Ingredient.objects.get_or_create(
            name=ing_data['name'],
            defaults=ing_data
        )
        created_ingredients.append(ingredient)
        print(f"{'✓ تم إنشاء' if created else '✓ موجود'} مكون: {ingredient.name}")
    
    return created_ingredients

def create_feed_types():
    """إنشاء أنواع الأعلاف"""
    feed_types_data = [
        {'name': 'علف دجاج بياض', 'target_animal': 'دجاج بياض', 'description': 'علف مخصص للدجاج البياض عالي الإنتاج'},
        {'name': 'علف دجاج لاحم', 'target_animal': 'دجاج لاحم', 'description': 'علف مخصص لتسمين الدجاج'},
        {'name': 'علف أبقار حلوب', 'target_animal': 'أبقار حلوب', 'description': 'علف مخصص للأبقار الحلوب'},
        {'name': 'علف أغنام', 'target_animal': 'أغنام', 'description': 'علف مخصص للأغنام والماعز'},
        {'name': 'علف أسماك', 'target_animal': 'أسماك', 'description': 'علف مخصص لتربية الأسماك'},
    ]
    
    created_types = []
    for type_data in feed_types_data:
        feed_type, created = FeedType.objects.get_or_create(
            name=type_data['name'],
            defaults=type_data
        )
        created_types.append(feed_type)
        print(f"{'✓ تم إنشاء' if created else '✓ موجود'} نوع علف: {feed_type.name}")
    
    return created_types

def create_customers():
    """إنشاء عملاء تجريبيين"""
    customers_data = [
        {
            'name': 'أحمد محمد علي',
            'company_name': 'مزرعة الأمل للدواجن',
            'phone': '07901234567',
            'email': '<EMAIL>',
            'city': 'بغداد',
            'credit_limit': Decimal('5000.000'),
            'address': 'منطقة الدورة - بغداد'
        },
        {
            'name': 'فاطمة حسن محمود',
            'company_name': 'مزرعة النور للأبقار',
            'phone': '07801234567',
            'email': '<EMAIL>',
            'city': 'البصرة',
            'credit_limit': Decimal('8000.000'),
            'address': 'قضاء أبي الخصيب - البصرة'
        },
        {
            'name': 'محمد عبد الله',
            'company_name': 'مزرعة الخير للأغنام',
            'phone': '07701234567',
            'email': '<EMAIL>',
            'city': 'الموصل',
            'credit_limit': Decimal('3000.000'),
            'address': 'ناحية الحمدانية - نينوى'
        },
    ]
    
    created_customers = []
    for cust_data in customers_data:
        customer, created = Customer.objects.get_or_create(
            phone=cust_data['phone'],
            defaults=cust_data
        )
        created_customers.append(customer)
        print(f"{'✓ تم إنشاء' if created else '✓ موجود'} عميل: {customer.name}")
    
    return created_customers

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إضافة البيانات التجريبية...")
    print("=" * 50)
    
    # إنشاء فئات المكونات
    print("\n📁 إنشاء فئات المكونات:")
    categories = create_ingredient_categories()
    
    # إنشاء المكونات
    print("\n🌾 إنشاء المكونات:")
    ingredients = create_ingredients()
    
    # إنشاء أنواع الأعلاف
    print("\n🍖 إنشاء أنواع الأعلاف:")
    feed_types = create_feed_types()
    
    # إنشاء العملاء
    print("\n👥 إنشاء العملاء:")
    customers = create_customers()
    
    print("\n" + "=" * 50)
    print("✅ تم إنشاء البيانات التجريبية بنجاح!")
    print(f"📊 الإحصائيات:")
    print(f"   • فئات المكونات: {len(categories)}")
    print(f"   • المكونات: {len(ingredients)}")
    print(f"   • أنواع الأعلاف: {len(feed_types)}")
    print(f"   • العملاء: {len(customers)}")
    print("\n🌐 يمكنك الآن تسجيل الدخول إلى النظام واستكشاف البيانات")
    print("📧 بيانات تسجيل الدخول:")
    print("   • اسم المستخدم: admin")
    print("   • كلمة المرور: admin123456")

if __name__ == '__main__':
    main()
