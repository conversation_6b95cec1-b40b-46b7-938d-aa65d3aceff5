from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _


class ProductionBatch(models.Model):
    """دفعات الإنتاج"""
    batch_number = models.CharField(_('رقم الدفعة'), max_length=20, unique=True)
    mixture = models.ForeignKey(
        'mixtures.FeedMixture',
        on_delete=models.CASCADE,
        verbose_name=_('خلطة العلف'),
        related_name='production_batches'
    )
    quantity_produced_kg = models.DecimalField(
        _('الكمية المنتجة (كغم)'),
        max_digits=10,
        decimal_places=2
    )
    production_date = models.DateTimeField(_('تاريخ الإنتاج'))
    produced_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('أنتج بواسطة'),
        related_name='production_batches'
    )

    # تكاليف الإنتاج
    material_cost = models.DecimalField(
        _('تكلفة المواد (دينار)'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    labor_cost = models.DecimalField(
        _('تكلفة العمالة (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    overhead_cost = models.DecimalField(
        _('التكاليف العامة (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    total_cost = models.DecimalField(
        _('التكلفة الإجمالية (دينار أردني)'),
        max_digits=12,
        decimal_places=3,
        default=0,
        editable=False
    )

    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('دفعة إنتاج')
        verbose_name_plural = _('دفعات الإنتاج')
        ordering = ['-production_date']

    def __str__(self):
        return f"{self.batch_number} - {self.mixture.name}"

    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية
        self.total_cost = self.material_cost + self.labor_cost + self.overhead_cost

        if not self.batch_number:
            # إنشاء رقم دفعة تلقائي
            from django.utils import timezone
            today = timezone.now().date()
            count = ProductionBatch.objects.filter(production_date__date=today).count() + 1
            self.batch_number = f"BATCH-{today.strftime('%Y%m%d')}-{count:04d}"

        super().save(*args, **kwargs)

    @property
    def cost_per_kg(self):
        """التكلفة لكل كيلوغرام"""
        if self.quantity_produced_kg > 0:
            return self.total_cost / self.quantity_produced_kg
        return 0


class SalesReport(models.Model):
    """تقارير المبيعات"""
    report_date = models.DateField(_('تاريخ التقرير'))
    total_orders = models.IntegerField(_('إجمالي الطلبات'), default=0)
    total_revenue = models.DecimalField(
        _('إجمالي الإيرادات (دينار أردني)'),
        max_digits=15,
        decimal_places=3,
        default=0
    )
    total_cost = models.DecimalField(
        _('إجمالي التكاليف (دينار)'),
        max_digits=15,
        decimal_places=3,
        default=0
    )
    total_profit = models.DecimalField(
        _('إجمالي الربح (دينار)'),
        max_digits=15,
        decimal_places=3,
        default=0,
        editable=False
    )

    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('تقرير مبيعات')
        verbose_name_plural = _('تقارير المبيعات')
        ordering = ['-report_date']
        unique_together = ['report_date']

    def __str__(self):
        return f"تقرير مبيعات {self.report_date}"

    def save(self, *args, **kwargs):
        # حساب الربح
        self.total_profit = self.total_revenue - self.total_cost
        super().save(*args, **kwargs)
