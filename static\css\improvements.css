/* تحسينات إضافية للواجهة */

/* تحسين الأنيميشن */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين الأزرار */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

/* تحسين الكروت */
.card-enhanced {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسين الجداول */
.table-enhanced {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table-enhanced tbody tr {
    transition: all 0.3s ease;
}

.table-enhanced tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

/* تحسين النماذج */
.form-enhanced .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.form-enhanced .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: white;
    transform: translateY(-2px);
}

.form-enhanced .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تحسين الإشعارات */
.alert-enhanced {
    border: none;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success.alert-enhanced {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger.alert-enhanced {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning.alert-enhanced {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info.alert-enhanced {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تحسين الإحصائيات */
.stat-card-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card-enhanced:hover {
    transform: translateY(-10px) scale(1.05);
}

.stat-card-enhanced::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.stat-card-enhanced:hover::before {
    top: -25%;
    right: -25%;
}

.stat-value-enhanced {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label-enhanced {
    font-size: 1rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* تحسين التنقل */
.navbar-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.navbar-enhanced .navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-enhanced .nav-link {
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 5px;
}

.navbar-enhanced .nav-link:hover {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* تحسين الشريط الجانبي */
.sidebar-enhanced {
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.sidebar-enhanced .nav-link {
    border-radius: 10px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.sidebar-enhanced .nav-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: translateX(10px);
}

.sidebar-enhanced .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* تحسين التحميل */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الاستجابة للهواتف */
@media (max-width: 768px) {
    .stat-card-enhanced {
        margin-bottom: 20px;
    }
    
    .table-enhanced {
        font-size: 0.9rem;
    }
    
    .btn-enhanced {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* تحسين الطباعة */
@media print {
    .btn-enhanced,
    .sidebar-enhanced,
    .navbar-enhanced {
        display: none !important;
    }
    
    .card-enhanced {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
