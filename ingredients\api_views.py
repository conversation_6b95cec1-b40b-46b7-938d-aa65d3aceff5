from rest_framework import generics, filters, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, F
from .models import Ingredient, IngredientCategory
from .serializers import (
    IngredientListSerializer, IngredientDetailSerializer,
    IngredientCreateUpdateSerializer, IngredientCategorySerializer
)


class IngredientCategoryListCreateView(generics.ListCreateAPIView):
    """قائمة وإنشاء فئات المكونات"""
    queryset = IngredientCategory.objects.all()
    serializer_class = IngredientCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class IngredientCategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """تفاصيل وتحديث وحذف فئة المكونات"""
    queryset = IngredientCategory.objects.all()
    serializer_class = IngredientCategorySerializer
    permission_classes = [IsAuthenticated]


class IngredientListCreateView(generics.ListCreateAPIView):
    """قائمة وإنشاء المكونات"""
    queryset = Ingredient.objects.select_related('category')
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description', 'supplier']
    ordering_fields = ['name', 'cost_per_kg', 'current_stock', 'created_at']
    ordering = ['name']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return IngredientCreateUpdateSerializer
        return IngredientListSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # فلتر حالة المخزون
        stock_status = self.request.query_params.get('stock_status')
        if stock_status:
            if stock_status == 'available':
                queryset = queryset.filter(current_stock__gt=F('minimum_stock'))
            elif stock_status == 'low':
                queryset = queryset.filter(
                    current_stock__lte=F('minimum_stock'),
                    current_stock__gt=0
                )
            elif stock_status == 'out':
                queryset = queryset.filter(current_stock=0)
        
        # فلتر النسب الغذائية
        min_protein = self.request.query_params.get('min_protein')
        max_protein = self.request.query_params.get('max_protein')
        if min_protein:
            queryset = queryset.filter(protein_percentage__gte=min_protein)
        if max_protein:
            queryset = queryset.filter(protein_percentage__lte=max_protein)
        
        min_energy = self.request.query_params.get('min_energy')
        max_energy = self.request.query_params.get('max_energy')
        if min_energy:
            queryset = queryset.filter(energy_kcal_per_kg__gte=min_energy)
        if max_energy:
            queryset = queryset.filter(energy_kcal_per_kg__lte=max_energy)
        
        return queryset


class IngredientDetailView(generics.RetrieveUpdateDestroyAPIView):
    """تفاصيل وتحديث وحذف المكون"""
    queryset = Ingredient.objects.select_related('category')
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return IngredientCreateUpdateSerializer
        return IngredientDetailSerializer
    
    def perform_destroy(self, instance):
        """حذف ناعم - تعطيل المكون بدلاً من حذفه"""
        instance.is_active = False
        instance.save()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ingredient_stats(request):
    """إحصائيات المكونات"""
    total_ingredients = Ingredient.objects.filter(is_active=True).count()
    total_categories = IngredientCategory.objects.count()
    
    # إحصائيات المخزون
    low_stock_count = Ingredient.objects.filter(
        current_stock__lte=F('minimum_stock'),
        current_stock__gt=0,
        is_active=True
    ).count()
    
    out_of_stock_count = Ingredient.objects.filter(
        current_stock=0,
        is_active=True
    ).count()
    
    available_count = Ingredient.objects.filter(
        current_stock__gt=F('minimum_stock'),
        is_active=True
    ).count()
    
    # إحصائيات التكلفة
    from django.db.models import Avg, Sum
    avg_cost = Ingredient.objects.filter(is_active=True).aggregate(
        avg_cost=Avg('cost_per_kg')
    )['avg_cost'] or 0
    
    total_stock_value = Ingredient.objects.filter(is_active=True).aggregate(
        total_value=Sum(F('current_stock') * F('cost_per_kg'))
    )['total_value'] or 0
    
    return Response({
        'total_ingredients': total_ingredients,
        'total_categories': total_categories,
        'stock_status': {
            'available': available_count,
            'low_stock': low_stock_count,
            'out_of_stock': out_of_stock_count
        },
        'cost_stats': {
            'average_cost_per_kg': round(float(avg_cost), 3),
            'total_stock_value': round(float(total_stock_value), 3)
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_stock(request, pk):
    """تحديث المخزون"""
    try:
        ingredient = Ingredient.objects.get(pk=pk, is_active=True)
    except Ingredient.DoesNotExist:
        return Response(
            {'error': 'المكون غير موجود'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    action = request.data.get('action')  # 'add' أو 'subtract' أو 'set'
    quantity = request.data.get('quantity')
    
    if not action or quantity is None:
        return Response(
            {'error': 'يجب تحديد العملية والكمية'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        quantity = float(quantity)
        if quantity < 0:
            return Response(
                {'error': 'الكمية يجب أن تكون موجبة'},
                status=status.HTTP_400_BAD_REQUEST
            )
    except (ValueError, TypeError):
        return Response(
            {'error': 'الكمية يجب أن تكون رقماً'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    old_stock = ingredient.current_stock
    
    if action == 'add':
        ingredient.current_stock += quantity
    elif action == 'subtract':
        if ingredient.current_stock < quantity:
            return Response(
                {'error': 'الكمية المطلوب خصمها أكبر من المخزون المتاح'},
                status=status.HTTP_400_BAD_REQUEST
            )
        ingredient.current_stock -= quantity
    elif action == 'set':
        ingredient.current_stock = quantity
    else:
        return Response(
            {'error': 'عملية غير صحيحة. استخدم: add, subtract, أو set'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    ingredient.save()
    
    return Response({
        'message': 'تم تحديث المخزون بنجاح',
        'ingredient': {
            'id': ingredient.id,
            'name': ingredient.name,
            'old_stock': float(old_stock),
            'new_stock': float(ingredient.current_stock),
            'stock_status': ingredient.stock_status
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def low_stock_ingredients(request):
    """المكونات منخفضة المخزون"""
    ingredients = Ingredient.objects.filter(
        current_stock__lte=F('minimum_stock'),
        is_active=True
    ).select_related('category').order_by('current_stock')
    
    serializer = IngredientListSerializer(ingredients, many=True)
    return Response(serializer.data)
